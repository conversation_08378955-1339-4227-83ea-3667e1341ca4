#!/usr/bin/env python3
"""
Demo script for the new pluggable scraper system.
Shows how the system automatically discovers and runs collectors.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from arep.collectors import PluggableDataCollector, get_all_collectors, list_available_collectors
from arep.utils.logger import get_logger

logger = get_logger(__name__)


async def demo_registry_system():
    """Demonstrate the registry system discovering collectors."""
    print("🔍 Discovering available collectors...")
    
    # List all available collectors
    collector_names = list_available_collectors()
    print(f"Found {len(collector_names)} collectors: {collector_names}")
    
    # Get all collector instances
    collectors = get_all_collectors()
    print("\n📋 Collector details:")
    for collector in collectors:
        print(f"  - {collector.source_name} ({collector.__class__.__name__})")
    
    return collectors


async def demo_pluggable_collector():
    """Demonstrate the PluggableDataCollector in action."""
    print("\n🚀 Testing PluggableDataCollector...")
    
    collector = PluggableDataCollector()
    print(f"Initialized with collectors: {collector.get_collector_names()}")
    
    # Note: This would make real HTTP requests to Product Hunt and ArXiv
    # For demo purposes, we'll just show the setup
    print("\n⚠️  Note: To avoid making real HTTP requests in this demo,")
    print("   we're not calling collector.collect(). In a real scenario:")
    print("   entities = await collector.collect()")
    print("   This would fetch real data from Product Hunt and ArXiv!")
    
    return collector


async def demo_adding_new_collector():
    """Show how easy it is to add a new collector."""
    print("\n➕ Adding a new collector is simple!")
    print("Just create a new file in arep/collectors/ that inherits from BaseCollector:")
    
    example_code = '''
# arep/collectors/my_new_source.py
from arep.collectors.base import BaseCollector
from arep.models import MinimalEntity
from datetime import datetime

class MyNewSourceCollector(BaseCollector):
    source_name = "my_new_source"
    
    async def collect(self, session):
        # Your scraping logic here
        return [
            MinimalEntity(
                name="Example Tool",
                url="https://example.com",
                source=self.source_name,
                discovered_at=datetime.utcnow()
            )
        ]
'''
    
    print(example_code)
    print("That's it! The registry will automatically discover and load it.")


async def demo_backward_compatibility():
    """Show that the old system still works."""
    print("\n🔄 Backward compatibility with existing MinimalDataCollector:")
    
    from arep.collectors import MinimalDataCollector
    
    old_collector = MinimalDataCollector()
    print(f"Old system has {len(old_collector.scrapers)} scrapers")
    
    # The old collector can also use the new system
    print("Old collector can also use new system via collect_with_new_system()")
    print("This allows gradual migration to the new architecture.")


async def main():
    """Run the complete demo."""
    print("🎯 Pluggable Scraper System Demo")
    print("=" * 50)
    
    try:
        # Demo the registry system
        await demo_registry_system()
        
        # Demo the pluggable collector
        await demo_pluggable_collector()
        
        # Show how to add new collectors
        await demo_adding_new_collector()
        
        # Show backward compatibility
        await demo_backward_compatibility()
        
        print("\n✅ Demo completed successfully!")
        print("\nKey benefits of the pluggable system:")
        print("  • Automatic discovery of new collectors")
        print("  • No need to modify core orchestrator code")
        print("  • Easy to add new data sources")
        print("  • Concurrent execution of all collectors")
        print("  • Built-in deduplication")
        print("  • Error handling for individual collectors")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"\n❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
