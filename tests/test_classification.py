"""
Tests for the classification system components.
Tests individual classifiers, content analysis, and LLM integration.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from uuid import uuid4

from arep.models import MinimalEntity, ClassificationResult
from arep.classification.classifier import (
    URLPatternClassifier, 
    NameBasedClassifier, 
    ContentAnalysisClassifier,
    EntityTypeClassifier
)
from arep.classification.llm_classifier import LLMClassifier, HybridLLMClassifier
from arep.utils.content_scraper import ContentExtractor, ContentAnalyzer


class TestURLPatternClassifier:
    """Test cases for URL pattern classifier."""
    
    @pytest.fixture
    def classifier(self):
        """Create URL pattern classifier."""
        return URLPatternClassifier()
    
    @pytest.fixture
    def test_entities(self):
        """Create test entities with different URL patterns."""
        return [
            MinimalEntity(
                name="OpenAI API",
                url="https://api.openai.com",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="ML Course",
                url="https://coursera.org/learn/machine-learning",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="GitHub Repo",
                url="https://github.com/user/repo",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="ArXiv Paper",
                url="https://arxiv.org/abs/2301.12345",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
        ]
    
    @pytest.mark.asyncio
    async def test_tool_classification(self, classifier):
        """Test classification of AI tools."""
        entity = MinimalEntity(
            name="ChatGPT",
            url="https://chat.openai.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        assert result.entity_type == "tool"
        assert result.confidence > 0.0
        assert "pattern" in result.reasoning.lower()
    
    @pytest.mark.asyncio
    async def test_course_classification(self, classifier):
        """Test classification of courses."""
        entity = MinimalEntity(
            name="Deep Learning Course",
            url="https://udemy.com/course/deep-learning",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        assert result.entity_type == "course"
        assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_software_classification(self, classifier):
        """Test classification of software."""
        entity = MinimalEntity(
            name="TensorFlow",
            url="https://github.com/tensorflow/tensorflow",
            source="test",
            discovered_at=datetime.utcnow(),
        )

        result = await classifier.classify(entity)

        # GitHub URLs can be classified as either software or platform
        assert result.entity_type in ["software", "platform"]
        assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_research_paper_classification(self, classifier):
        """Test classification of research papers."""
        entity = MinimalEntity(
            name="Attention Is All You Need",
            url="https://arxiv.org/abs/1706.03762",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        assert result.entity_type == "research_paper"
        assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_unknown_pattern(self, classifier):
        """Test classification with unknown patterns."""
        entity = MinimalEntity(
            name="Unknown Entity",
            url="https://unknown-domain.xyz",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        # Should default to tool with low confidence
        assert result.entity_type == "tool"
        assert result.confidence <= 0.2


class TestNameBasedClassifier:
    """Test cases for name-based classifier."""
    
    @pytest.fixture
    def classifier(self):
        """Create name-based classifier."""
        return NameBasedClassifier()
    
    @pytest.mark.asyncio
    async def test_ai_tool_names(self, classifier):
        """Test classification based on AI tool names."""
        entities = [
            MinimalEntity(
                name="GPT-4 API Platform",
                url="https://example.com",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="ML Studio Pro",
                url="https://example.com",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
        ]
        
        for entity in entities:
            result = await classifier.classify(entity)
            assert result.entity_type == "tool"
            assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_course_names(self, classifier):
        """Test classification based on course names."""
        entity = MinimalEntity(
            name="Machine Learning Course for Beginners",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        assert result.entity_type == "course"
        assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_research_names(self, classifier):
        """Test classification based on research paper names."""
        entity = MinimalEntity(
            name="A Research Study on Neural Networks",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        result = await classifier.classify(entity)
        
        assert result.entity_type == "research_paper"
        assert result.confidence > 0.0


class TestContentAnalysisClassifier:
    """Test cases for content analysis classifier."""
    
    @pytest.fixture
    def classifier(self):
        """Create content analysis classifier."""
        return ContentAnalysisClassifier()
    
    @pytest.mark.asyncio
    async def test_content_analysis_with_mock_data(self, classifier):
        """Test content analysis with mocked webpage data."""
        entity = MinimalEntity(
            name="AI Tool",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        # Mock the content fetching
        mock_html = """
        <html>
        <head>
            <title>AI Tool - Machine Learning Platform</title>
            <meta name="description" content="Advanced AI tool for machine learning">
            <meta name="keywords" content="ai, tool, platform, machine learning">
        </head>
        <body>
            <h1>AI Tool</h1>
            <p>This is an AI platform with features like:</p>
            <ul>
                <li>API access</li>
                <li>Pricing plans</li>
                <li>Demo available</li>
            </ul>
        </body>
        </html>
        """
        
        from bs4 import BeautifulSoup
        
        with patch.object(classifier, '_fetch_page_content') as mock_fetch:
            mock_fetch.return_value = (mock_html, BeautifulSoup(mock_html, 'html.parser'))
            
            result = await classifier.classify(entity)
            
            assert isinstance(result, ClassificationResult)
            assert result.entity_type
            assert result.confidence > 0.0
    
    @pytest.mark.asyncio
    async def test_content_analysis_failure(self, classifier):
        """Test content analysis when page fetch fails."""
        entity = MinimalEntity(
            name="Test Entity",
            url="https://invalid-url.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        with patch.object(classifier, '_fetch_page_content') as mock_fetch:
            mock_fetch.return_value = None
            
            result = await classifier.classify(entity)
            
            assert result.entity_type == "tool"  # Default fallback
            assert result.confidence <= 0.2
            assert "could not fetch" in result.reasoning.lower()


class TestLLMClassifier:
    """Test cases for LLM-based classifier."""
    
    @pytest.fixture
    def classifier(self):
        """Create LLM classifier with mock API key."""
        return LLMClassifier(api_key="test-key")
    
    @pytest.mark.asyncio
    async def test_llm_classification_success(self, classifier):
        """Test successful LLM classification."""
        entity = MinimalEntity(
            name="OpenAI GPT-4",
            url="https://openai.com/gpt-4",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        # Mock content extraction
        mock_content = {
            "title": "GPT-4 - OpenAI",
            "description": "Advanced AI language model",
            "content_text": "GPT-4 is a powerful AI tool for natural language processing",
            "features": ["API access", "Multiple models", "Fine-tuning"],
        }
        
        # Mock OpenAI API response
        mock_api_response = {
            "entity_type": "tool",
            "confidence": 0.9,
            "reasoning": "This is clearly an AI tool based on the content",
            "alternative_types": ["platform"]
        }
        
        with patch.object(classifier.content_analyzer.extractor, 'extract_page_data') as mock_extract, \
             patch.object(classifier, '_call_openai_api') as mock_api:
            
            mock_extract.return_value = mock_content
            mock_api.return_value = mock_api_response
            
            result = await classifier.classify(entity)
            
            assert result.entity_type == "tool"
            assert result.confidence == 0.9
            assert "llm analysis" in result.reasoning.lower()
    
    @pytest.mark.asyncio
    async def test_llm_api_failure(self, classifier):
        """Test LLM classification when API fails."""
        entity = MinimalEntity(
            name="Test Entity",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        # Mock content extraction success but API failure
        mock_content = {"title": "Test", "description": "Test description"}
        
        with patch.object(classifier.content_analyzer.extractor, 'extract_page_data') as mock_extract, \
             patch.object(classifier, '_call_openai_api') as mock_api:
            
            mock_extract.return_value = mock_content
            mock_api.return_value = None  # API failure
            
            result = await classifier.classify(entity)
            
            assert result.entity_type == "tool"  # Fallback
            assert result.confidence <= 0.3
            assert "api call failed" in result.reasoning.lower()
    
    @pytest.mark.asyncio
    async def test_llm_no_api_key(self):
        """Test LLM classifier without API key."""
        classifier = LLMClassifier(api_key=None)
        
        entity = MinimalEntity(
            name="Test Entity",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        with patch.object(classifier.content_analyzer.extractor, 'extract_page_data') as mock_extract:
            mock_extract.return_value = {"title": "Test"}
            
            result = await classifier.classify(entity)
            
            assert result.confidence <= 0.3
            assert "api call failed" in result.reasoning.lower()


class TestHybridLLMClassifier:
    """Test cases for hybrid LLM classifier."""
    
    @pytest.fixture
    def classifier(self):
        """Create hybrid LLM classifier."""
        return HybridLLMClassifier(api_key="test-key", llm_threshold=0.7)
    
    @pytest.mark.asyncio
    async def test_high_confidence_traditional(self, classifier):
        """Test using traditional classification for high confidence."""
        entity = MinimalEntity(
            name="GitHub Repository",
            url="https://github.com/user/repo",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        # Mock high confidence traditional result
        with patch.object(classifier.url_classifier, 'classify') as mock_url, \
             patch.object(classifier.name_classifier, 'classify') as mock_name:
            
            mock_url.return_value = ClassificationResult(
                entity_type="software",
                entity_type_id=uuid4(),
                confidence=0.8,
                reasoning="GitHub URL pattern",
            )
            mock_name.return_value = ClassificationResult(
                entity_type="software",
                entity_type_id=uuid4(),
                confidence=0.6,
                reasoning="Repository name",
            )
            
            result = await classifier.classify(entity)
            
            assert result.entity_type == "software"
            assert result.confidence >= 0.7
    
    @pytest.mark.asyncio
    async def test_low_confidence_llm_fallback(self, classifier):
        """Test falling back to LLM for low confidence."""
        entity = MinimalEntity(
            name="Ambiguous Entity",
            url="https://example.com",
            source="test",
            discovered_at=datetime.utcnow(),
        )
        
        # Mock low confidence traditional results
        low_confidence_result = ClassificationResult(
            entity_type="tool",
            entity_type_id=uuid4(),
            confidence=0.3,
            reasoning="Low confidence classification",
        )
        
        # Mock high confidence LLM result
        high_confidence_llm_result = ClassificationResult(
            entity_type="platform",
            entity_type_id=uuid4(),
            confidence=0.8,
            reasoning="LLM analysis shows this is a platform",
        )
        
        with patch.object(classifier.url_classifier, 'classify') as mock_url, \
             patch.object(classifier.name_classifier, 'classify') as mock_name, \
             patch.object(classifier.llm_classifier, 'classify') as mock_llm:
            
            mock_url.return_value = low_confidence_result
            mock_name.return_value = low_confidence_result
            mock_llm.return_value = high_confidence_llm_result
            
            result = await classifier.classify(entity)
            
            assert result.entity_type == "platform"
            assert result.confidence >= 0.7


class TestContentScraper:
    """Test cases for content scraping utilities."""
    
    @pytest.fixture
    def extractor(self):
        """Create content extractor."""
        return ContentExtractor()
    
    @pytest.fixture
    def analyzer(self):
        """Create content analyzer."""
        return ContentAnalyzer()
    
    @pytest.mark.asyncio
    async def test_content_extraction_mock(self, extractor):
        """Test content extraction with mocked HTTP response."""
        mock_html = """
        <html>
        <head>
            <title>Test Page</title>
            <meta name="description" content="Test description">
            <meta name="keywords" content="test, page, content">
        </head>
        <body>
            <h1>Main Heading</h1>
            <h2>Subheading</h2>
            <p>This is test content with some features:</p>
            <ul>
                <li>Feature 1</li>
                <li>Feature 2</li>
            </ul>
            <a href="https://external.com">External Link</a>
            <a href="/internal">Internal Link</a>
        </body>
        </html>
        """
        
        with patch('aiohttp.ClientSession.get') as mock_get:
            # Mock the response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text = AsyncMock(return_value=mock_html)
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await extractor.extract_page_data("https://example.com")
            
            assert "error" not in result
            assert result["title"] == "Test Page"
            assert result["description"] == "Test description"
            assert "test" in result["keywords"]
            assert len(result["headings"]["h1"]) == 1
            assert len(result["headings"]["h2"]) == 1
    
    @pytest.mark.asyncio
    async def test_content_analysis_for_classification(self, analyzer):
        """Test content analysis for classification purposes."""
        mock_content = {
            "title": "AI Tool Platform",
            "description": "Advanced machine learning platform",
            "content_text": "This platform provides AI tools and APIs for developers",
            "features": ["API access", "Dashboard", "Analytics"],
            "social_links": {"github": "https://github.com/company"},
        }
        
        with patch.object(analyzer.extractor, 'extract_page_data') as mock_extract:
            mock_extract.return_value = mock_content
            
            analysis = await analyzer.analyze_for_classification("https://example.com")
            
            assert "error" not in analysis
            assert "classification_signals" in analysis
            assert "content_type_indicators" in analysis
            assert "business_model_signals" in analysis


@pytest.mark.asyncio
async def test_classification_integration():
    """Test integration between different classification components."""
    # Create test entity
    entity = MinimalEntity(
        name="TensorFlow",
        url="https://github.com/tensorflow/tensorflow",
        source="test",
        discovered_at=datetime.utcnow(),
    )
    
    # Test multiple classifiers
    url_classifier = URLPatternClassifier()
    name_classifier = NameBasedClassifier()
    main_classifier = EntityTypeClassifier()
    
    # Get results from individual classifiers
    url_result = await url_classifier.classify(entity)
    name_result = await name_classifier.classify(entity)
    main_result = await main_classifier.classify(entity)
    
    # URL classifier should classify as software or platform (GitHub can be either)
    assert url_result.entity_type in ["software", "platform"]

    # Name classifier might classify as tool (TensorFlow doesn't have obvious software keywords)
    assert name_result.entity_type in ["software", "platform", "tool"]

    # Main classifier should provide a reasonable classification
    assert main_result.entity_type in ["software", "platform", "tool"]

    # Main classifier should have reasonable confidence
    assert main_result.confidence > 0.0
