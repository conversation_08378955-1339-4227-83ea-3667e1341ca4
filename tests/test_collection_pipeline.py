"""
Tests for the data collection and classification pipeline.
Tests the complete workflow from data collection to entity classification.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from uuid import uuid4

from arep.models import MinimalEntity, ClassificationResult, ScrapingResult
from arep.collectors.collector import <PERSON>malDataCollector, AdvancedDataCollector
from arep.collectors.base import MockScraper
from arep.classification.classifier import EntityTypeClassifier, URLPatternClassifier, NameBasedClassifier
from arep.classification.entity_mapping import get_entity_type_mapping
from arep.classification.confidence import get_confidence_scorer


class TestDataCollection:
    """Test cases for data collection functionality."""
    
    @pytest.fixture
    def mock_scraper(self):
        """Create a mock scraper for testing."""
        return MockScraper()
    
    @pytest.fixture
    def collector(self):
        """Create a data collector for testing."""
        return MinimalDataCollector(max_concurrent_scrapers=2)
    
    @pytest.fixture
    def advanced_collector(self):
        """Create an advanced data collector for testing."""
        return AdvancedDataCollector(max_concurrent_scrapers=2, enable_deduplication=True)
    
    @pytest.mark.asyncio
    async def test_mock_scraper(self, mock_scraper):
        """Test the mock scraper functionality."""
        async with mock_scraper:
            result = await mock_scraper.scrape()
            
            assert isinstance(result, ScrapingResult)
            assert result.success is True
            assert result.entities_found > 0
            assert len(result.entities_data) == result.entities_found
            assert result.source_name == "mock_scraper"
    
    @pytest.mark.asyncio
    async def test_collector_initialization(self, collector):
        """Test collector initialization."""
        assert len(collector.scrapers) > 0
        assert "mock_scraper" in collector.get_scraper_names()
        assert collector.max_concurrent_scrapers == 2
    
    @pytest.mark.asyncio
    async def test_collect_from_single_scraper(self, collector, mock_scraper):
        """Test collecting from a single scraper."""
        result = await collector.collect_from_scraper(mock_scraper)
        
        assert isinstance(result, ScrapingResult)
        assert result.success is True
        assert result.entities_found > 0
    
    @pytest.mark.asyncio
    async def test_collect_all_scrapers(self, collector):
        """Test collecting from all scrapers."""
        results = await collector.collect_all()
        
        assert len(results) > 0
        assert all(isinstance(result, ScrapingResult) for result in results)
        
        # Check that at least one scraper succeeded
        successful_results = [r for r in results if r.success]
        assert len(successful_results) > 0
    
    @pytest.mark.asyncio
    async def test_collect_minimal_entities(self, collector):
        """Test collecting minimal entities."""
        entities = await collector.collect()
        
        assert len(entities) > 0
        assert all(isinstance(entity, MinimalEntity) for entity in entities)
        
        # Check entity properties
        for entity in entities:
            assert entity.name
            assert entity.url
            assert entity.source
            assert entity.discovered_at
    
    @pytest.mark.asyncio
    async def test_advanced_collector_deduplication(self, advanced_collector):
        """Test deduplication in advanced collector."""
        # First collection
        entities1 = await advanced_collector.collect()
        
        # Second collection (should have duplicates removed)
        entities2 = await advanced_collector.collect()
        
        # Check that entities are collected
        assert len(entities1) > 0
        assert len(entities2) > 0
        
        # Check that deduplication is working (URLs should be unique within each collection)
        urls1 = [str(entity.url) for entity in entities1]
        urls2 = [str(entity.url) for entity in entities2]
        
        assert len(urls1) == len(set(urls1))  # No duplicates in first collection
        assert len(urls2) == len(set(urls2))  # No duplicates in second collection
    
    def test_collector_metrics(self, collector):
        """Test collector metrics functionality."""
        metrics = collector.get_metrics()
        assert hasattr(metrics, 'total_entities_discovered')
        
        scraper_metrics = collector.get_scraper_metrics()
        assert isinstance(scraper_metrics, dict)
        assert len(scraper_metrics) > 0
    
    def test_scraper_management(self, collector):
        """Test adding and removing scrapers."""
        initial_count = len(collector.scrapers)
        
        # Add a new scraper
        new_scraper = MockScraper()
        new_scraper.name = "test_scraper"
        collector.add_scraper(new_scraper)
        
        assert len(collector.scrapers) == initial_count + 1
        assert "test_scraper" in collector.get_scraper_names()
        
        # Remove the scraper
        removed = collector.remove_scraper("test_scraper")
        assert removed is True
        assert len(collector.scrapers) == initial_count
        assert "test_scraper" not in collector.get_scraper_names()
        
        # Try to remove non-existent scraper
        removed = collector.remove_scraper("non_existent")
        assert removed is False


class TestEntityClassification:
    """Test cases for entity classification functionality."""
    
    @pytest.fixture
    def sample_entities(self):
        """Create sample entities for testing."""
        return [
            MinimalEntity(
                name="OpenAI GPT-4",
                url="https://openai.com/gpt-4",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="Machine Learning Course",
                url="https://coursera.org/learn/machine-learning",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="AI Consulting Agency",
                url="https://ai-agency.com",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
            MinimalEntity(
                name="Research Paper on Neural Networks",
                url="https://arxiv.org/abs/1234.5678",
                source="test",
                discovered_at=datetime.utcnow(),
            ),
        ]
    
    @pytest.fixture
    def url_classifier(self):
        """Create URL pattern classifier."""
        return URLPatternClassifier()
    
    @pytest.fixture
    def name_classifier(self):
        """Create name-based classifier."""
        return NameBasedClassifier()
    
    @pytest.fixture
    def entity_classifier(self):
        """Create main entity classifier."""
        return EntityTypeClassifier()
    
    @pytest.mark.asyncio
    async def test_url_pattern_classifier(self, url_classifier, sample_entities):
        """Test URL pattern classification."""
        for entity in sample_entities:
            result = await url_classifier.classify(entity)
            
            assert isinstance(result, ClassificationResult)
            assert result.entity_type
            assert 0.0 <= result.confidence <= 1.0
            assert result.reasoning
    
    @pytest.mark.asyncio
    async def test_name_based_classifier(self, name_classifier, sample_entities):
        """Test name-based classification."""
        for entity in sample_entities:
            result = await name_classifier.classify(entity)
            
            assert isinstance(result, ClassificationResult)
            assert result.entity_type
            assert 0.0 <= result.confidence <= 1.0
            assert result.reasoning
    
    @pytest.mark.asyncio
    async def test_entity_classifier(self, entity_classifier, sample_entities):
        """Test main entity classifier."""
        for entity in sample_entities:
            result = await entity_classifier.classify(entity)
            
            assert isinstance(result, ClassificationResult)
            assert result.entity_type
            assert 0.0 <= result.confidence <= 1.0
            assert result.reasoning
    
    @pytest.mark.asyncio
    async def test_classification_consistency(self, entity_classifier, sample_entities):
        """Test that classification is consistent for the same entity."""
        entity = sample_entities[0]
        
        # Classify the same entity multiple times
        results = []
        for _ in range(3):
            result = await entity_classifier.classify(entity)
            results.append(result)
        
        # Check consistency
        entity_types = [r.entity_type for r in results]
        assert len(set(entity_types)) == 1  # All should be the same
        
        # Confidence should be similar (within 0.1)
        confidences = [r.confidence for r in results]
        max_confidence = max(confidences)
        min_confidence = min(confidences)
        assert max_confidence - min_confidence <= 0.1
    
    def test_classifier_management(self, entity_classifier):
        """Test adding and removing classifiers."""
        initial_count = len(entity_classifier.classifiers)
        
        # Add a new classifier
        from arep.classification.classifier import BaseClassifier
        
        class TestClassifier(BaseClassifier):
            def __init__(self):
                super().__init__("test_classifier")
            
            async def classify(self, entity):
                return ClassificationResult(
                    entity_type="tool",
                    entity_type_id=uuid4(),
                    confidence=0.5,
                    reasoning="Test classification",
                )
        
        test_classifier = TestClassifier()
        entity_classifier.add_classifier(test_classifier, weight=0.5)
        
        assert len(entity_classifier.classifiers) == initial_count + 1
        assert "test_classifier" in entity_classifier.get_classifier_names()
        
        # Remove the classifier
        removed = entity_classifier.remove_classifier("test_classifier")
        assert removed is True
        assert len(entity_classifier.classifiers) == initial_count


class TestEntityTypeMapping:
    """Test cases for entity type mapping."""
    
    def test_entity_type_mapping(self):
        """Test entity type mapping functionality."""
        mapping = get_entity_type_mapping()
        
        # Test getting UUID
        tool_uuid = mapping.get_uuid("tool")
        assert tool_uuid is not None
        
        # Test getting mapping
        tool_mapping = mapping.get_mapping("tool")
        assert tool_mapping is not None
        assert "uuid" in tool_mapping
        assert "name" in tool_mapping
        assert "description" in tool_mapping
        
        # Test validation
        assert mapping.validate_entity_type("tool") is True
        assert mapping.validate_entity_type("invalid_type") is False
        
        # Test getting all types
        all_types = mapping.get_all_types()
        assert len(all_types) > 0
        assert "tool" in all_types
        
        # Test categories
        categories = mapping.get_categories()
        assert len(categories) > 0
        
        tech_types = mapping.get_types_by_category("technology")
        assert len(tech_types) > 0
        assert "tool" in tech_types


class TestConfidenceScoring:
    """Test cases for confidence scoring."""
    
    @pytest.fixture
    def sample_entity(self):
        """Create a sample entity for testing."""
        return MinimalEntity(
            name="OpenAI GPT-4",
            url="https://openai.com/gpt-4",
            source="test",
            discovered_at=datetime.utcnow(),
        )
    
    @pytest.fixture
    def sample_results(self):
        """Create sample classification results."""
        return [
            ClassificationResult(
                entity_type="tool",
                entity_type_id=uuid4(),
                confidence=0.8,
                reasoning="URL pattern match",
            ),
            ClassificationResult(
                entity_type="tool",
                entity_type_id=uuid4(),
                confidence=0.7,
                reasoning="Name-based classification",
            ),
        ]
    
    def test_confidence_calculation(self, sample_entity, sample_results):
        """Test confidence score calculation."""
        scorer = get_confidence_scorer()
        
        confidence = scorer.calculate_confidence(
            entity=sample_entity,
            classification_results=sample_results,
        )
        
        assert 0.0 <= confidence <= 1.0
    
    def test_confidence_levels(self):
        """Test confidence level categorization."""
        scorer = get_confidence_scorer()
        
        assert scorer.get_confidence_level(0.9) == "high"
        assert scorer.get_confidence_level(0.7) == "medium"
        assert scorer.get_confidence_level(0.5) == "low"
        assert scorer.get_confidence_level(0.2) == "very_low"
    
    def test_llm_recommendation(self):
        """Test LLM usage recommendation."""
        scorer = get_confidence_scorer()
        
        assert scorer.should_use_llm(0.5) is True
        assert scorer.should_use_llm(0.8) is False
    
    def test_confidence_explanation(self, sample_entity, sample_results):
        """Test confidence explanation generation."""
        scorer = get_confidence_scorer()
        
        confidence = scorer.calculate_confidence(
            entity=sample_entity,
            classification_results=sample_results,
        )
        
        # Mock factors for explanation
        factors = {
            "classifier_agreement": 0.8,
            "signal_strength": 0.7,
            "content_quality": 0.6,
            "url_reliability": 0.9,
            "name_clarity": 0.8,
        }
        
        explanation = scorer.get_confidence_explanation(confidence, factors)
        assert isinstance(explanation, str)
        assert len(explanation) > 0


class TestIntegratedPipeline:
    """Test cases for the integrated collection and classification pipeline."""
    
    @pytest.mark.asyncio
    async def test_full_pipeline(self):
        """Test the complete pipeline from collection to classification."""
        # Initialize components
        collector = MinimalDataCollector()
        classifier = EntityTypeClassifier()
        
        # Collect entities
        entities = await collector.collect()
        assert len(entities) > 0
        
        # Classify entities
        classified_entities = []
        for entity in entities[:3]:  # Test with first 3 entities
            result = await classifier.classify(entity)
            classified_entities.append((entity, result))
        
        # Verify results
        assert len(classified_entities) == 3
        
        for entity, result in classified_entities:
            assert isinstance(entity, MinimalEntity)
            assert isinstance(result, ClassificationResult)
            assert result.entity_type
            assert 0.0 <= result.confidence <= 1.0
    
    @pytest.mark.asyncio
    async def test_pipeline_with_confidence_scoring(self):
        """Test pipeline with confidence scoring."""
        collector = MinimalDataCollector()
        classifier = EntityTypeClassifier()
        scorer = get_confidence_scorer()
        
        # Collect and classify
        entities = await collector.collect()
        entity = entities[0]
        
        # Get multiple classification results
        url_classifier = URLPatternClassifier()
        name_classifier = NameBasedClassifier()
        
        results = [
            await url_classifier.classify(entity),
            await name_classifier.classify(entity),
        ]
        
        # Calculate confidence
        confidence = scorer.calculate_confidence(entity, results)
        
        assert 0.0 <= confidence <= 1.0
        
        # Get confidence level
        level = scorer.get_confidence_level(confidence)
        assert level in ["high", "medium", "low", "very_low"]
    
    @pytest.mark.asyncio
    async def test_pipeline_error_handling(self):
        """Test pipeline error handling."""
        collector = MinimalDataCollector()
        
        # Test with empty scrapers
        collector.scrapers = []
        results = await collector.collect_all()
        assert len(results) == 0
        
        # Test classification with invalid entity
        classifier = EntityTypeClassifier()
        
        # Create entity with invalid URL (this should still work)
        try:
            invalid_entity = MinimalEntity(
                name="Test",
                url="https://invalid-url-that-does-not-exist.com",
                source="test",
                discovered_at=datetime.utcnow(),
            )
            result = await classifier.classify(invalid_entity)
            assert isinstance(result, ClassificationResult)
        except Exception as e:
            # If it fails, that's also acceptable for invalid URLs
            assert "url" in str(e).lower() or "invalid" in str(e).lower()


@pytest.mark.asyncio
async def test_performance_benchmarks():
    """Test performance benchmarks for the pipeline."""
    import time
    
    collector = MinimalDataCollector()
    classifier = EntityTypeClassifier()
    
    # Benchmark collection
    start_time = time.time()
    entities = await collector.collect()
    collection_time = time.time() - start_time
    
    assert collection_time < 10.0  # Should complete within 10 seconds
    assert len(entities) > 0
    
    # Benchmark classification
    if entities:
        start_time = time.time()
        result = await classifier.classify(entities[0])
        classification_time = time.time() - start_time
        
        assert classification_time < 5.0  # Should complete within 5 seconds
        assert isinstance(result, ClassificationResult)
