"""
Integration tests for the complete API submission workflow.
Tests the end-to-end functionality including client, validation, batch processing, and monitoring.
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

import pytest

from arep.api.batch import Batch<PERSON>onfig, BatchProcessor, BatchStrategy
from arep.api.client import AINavigatorClient
from arep.api.models import (BatchSubmissionResponse, Resource,
                             ResourceSubmissionResponse)
from arep.api.validation import ResponseValidator
from arep.monitoring.metrics import get_metrics_collector, reset_metrics


class TestAPIIntegration:
    """Integration tests for the complete API workflow."""

    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Reset metrics before each test
        reset_metrics()
        yield
        # Clean up after each test
        reset_metrics()

    @pytest.fixture
    def mock_client(self):
        """Create a mock API client."""
        client = MagicMock(spec=AINavigatorClient)
        client.submit_resource = AsyncMock()
        client.submit_resources_batch = AsyncMock()
        client.health_check = AsyncMock()
        client.get_submission_status = AsyncMock()
        client.update_resource = AsyncMock()
        return client

    @pytest.fixture
    def sample_resources(self):
        """Create sample resources for testing."""
        return [
            Resource(
                name=f"Test AI Tool {i}",
                website_url=f"https://example{i}.com",
                entity_type_id=uuid4(),
                short_description=f"A comprehensive test AI tool number {i} for integration testing",
                description=f"This is a longer description of test AI tool {i}.",
            )
            for i in range(5)
        ]

    @pytest.mark.asyncio
    async def test_single_resource_submission_workflow(
        self, mock_client, sample_resources
    ):
        """Test complete workflow for single resource submission."""
        resource = sample_resources[0]
        entity_id = uuid4()

        # Mock successful submission
        mock_response = ResourceSubmissionResponse(
            success=True,
            message="Resource submitted successfully",
            entity_id=entity_id,
            status="PENDING",
        )
        mock_client.submit_resource.return_value = mock_response

        # 1. Validate resource before submission
        validation_errors = ResponseValidator.validate_resource_data(resource)
        assert len(validation_errors) == 0

        # 2. Submit resource
        result = await mock_client.submit_resource(resource)

        # 3. Verify submission
        assert result.success is True
        assert result.entity_id == entity_id
        assert result.status == "PENDING"

        # 4. Verify client was called correctly
        mock_client.submit_resource.assert_called_once_with(resource)

        # 5. Check metrics
        metrics = get_metrics_collector()
        # Note: In real integration, metrics would be recorded by the actual client

    @pytest.mark.asyncio
    async def test_batch_submission_workflow(self, mock_client, sample_resources):
        """Test complete workflow for batch resource submission."""
        # Mock successful batch submission
        mock_responses = [
            ResourceSubmissionResponse(
                success=True,
                entity_id=uuid4(),
                status="PENDING",
            )
            for _ in sample_resources
        ]

        mock_batch_response = BatchSubmissionResponse(
            success=True,
            message="Batch submission completed successfully",
            total_submitted=len(sample_resources),
            successful_submissions=len(sample_resources),
            failed_submissions=0,
            submission_results=mock_responses,
        )
        mock_client.submit_resources_batch.return_value = mock_batch_response

        # 1. Validate all resources
        for resource in sample_resources:
            validation_errors = ResponseValidator.validate_resource_data(resource)
            assert len(validation_errors) == 0

        # 2. Submit batch
        result = await mock_client.submit_resources_batch(
            sample_resources,
            batch_size=3,
            delay_between_batches=0.1,
        )

        # 3. Verify batch submission
        assert result.success is True
        assert result.total_submitted == len(sample_resources)
        assert result.successful_submissions == len(sample_resources)
        assert result.failed_submissions == 0
        assert len(result.submission_results) == len(sample_resources)

        # 4. Verify client was called correctly
        mock_client.submit_resources_batch.assert_called_once()

    @pytest.mark.asyncio
    async def test_batch_processor_integration(self, mock_client, sample_resources):
        """Test integration with BatchProcessor."""
        # Configure batch processor
        config = BatchConfig(
            batch_size=2,
            strategy=BatchStrategy.CHUNKED,
            delay_between_batches=0.1,
            validate_before_submit=True,
        )

        processor = BatchProcessor(mock_client, config)

        # Mock individual submissions
        mock_responses = [
            ResourceSubmissionResponse(
                success=True,
                entity_id=uuid4(),
                status="PENDING",
            )
            for _ in sample_resources
        ]
        mock_client.submit_resource.side_effect = mock_responses

        # Process resources
        result = await processor.process_resources(sample_resources)

        # Verify results
        assert result.success is True
        assert result.total_submitted == len(sample_resources)
        assert result.successful_submissions == len(sample_resources)
        assert result.failed_submissions == 0

        # Verify all resources were submitted
        assert mock_client.submit_resource.call_count == len(sample_resources)

    @pytest.mark.asyncio
    async def test_batch_processor_with_failures(self, mock_client, sample_resources):
        """Test batch processor handling partial failures."""
        config = BatchConfig(
            batch_size=2,
            strategy=BatchStrategy.PARALLEL,
            validate_before_submit=True,
        )

        processor = BatchProcessor(mock_client, config)

        # Mock mixed success/failure responses
        mock_responses = [
            ResourceSubmissionResponse(success=True, entity_id=uuid4()),
            ResourceSubmissionResponse(success=False, message="Validation failed"),
            ResourceSubmissionResponse(success=True, entity_id=uuid4()),
            ResourceSubmissionResponse(success=False, message="Network error"),
            ResourceSubmissionResponse(success=True, entity_id=uuid4()),
        ]
        mock_client.submit_resource.side_effect = mock_responses

        # Process resources
        result = await processor.process_resources(sample_resources)

        # Verify results
        assert result.success is False  # Not all succeeded
        assert result.total_submitted == len(sample_resources)
        assert result.successful_submissions == 3
        assert result.failed_submissions == 2

    @pytest.mark.asyncio
    async def test_validation_integration(self, sample_resources):
        """Test validation integration with various scenarios."""
        # Test valid resource
        valid_resource = sample_resources[0]
        errors = ResponseValidator.validate_resource_data(valid_resource)
        assert len(errors) == 0

        # Test invalid resource
        invalid_resource = Resource(
            name="AI",  # Too short
            website_url="https://example.com",
            entity_type_id=uuid4(),
            short_description="Short",  # Too short
        )
        errors = ResponseValidator.validate_resource_data(invalid_resource)
        assert len(errors) > 0

        # Test response validation
        api_response = {
            "success": True,
            "id": str(uuid4()),
            "status": "PENDING",
            "message": "Resource created successfully",
        }

        validated_response = ResponseValidator.validate_submission_response(
            api_response
        )
        assert validated_response.success is True
        assert validated_response.entity_id is not None
        assert validated_response.status == "PENDING"

    @pytest.mark.asyncio
    async def test_error_handling_integration(self, mock_client, sample_resources):
        """Test error handling throughout the workflow."""
        resource = sample_resources[0]

        # Test authentication error
        from arep.api.client import AuthenticationError

        mock_client.submit_resource.side_effect = AuthenticationError("Invalid token")

        with pytest.raises(AuthenticationError):
            await mock_client.submit_resource(resource)

        # Test rate limiting error
        from arep.api.client import RateLimitError

        mock_client.submit_resource.side_effect = RateLimitError("Rate limit exceeded")

        with pytest.raises(RateLimitError):
            await mock_client.submit_resource(resource)

        # Test general API error
        from arep.api.client import APIClientError

        mock_client.submit_resource.side_effect = APIClientError("Server error")

        with pytest.raises(APIClientError):
            await mock_client.submit_resource(resource)

    @pytest.mark.asyncio
    async def test_monitoring_integration(self, mock_client, sample_resources):
        """Test monitoring and metrics integration."""
        metrics = get_metrics_collector()

        # Simulate API requests with metrics recording
        for i, resource in enumerate(sample_resources[:3]):
            success = i % 2 == 0  # Alternate success/failure
            response_time = 0.1 + (i * 0.05)
            status_code = 200 if success else 500

            metrics.record_api_request(
                endpoint="/entities",
                method="POST",
                success=success,
                response_time=response_time,
                status_code=status_code,
            )

        # Check metrics summary
        summary = metrics.get_summary()

        assert summary["api"]["total_requests"] == 3
        assert summary["api"]["successful_requests"] == 2
        assert summary["api"]["failed_requests"] == 1
        assert summary["api"]["success_rate_percent"] == 66.67

        # Check health status
        health = metrics.get_health_status()
        assert health["status"] in ["healthy", "degraded"]  # Depends on success rate

    @pytest.mark.asyncio
    async def test_retry_logic_integration(self, mock_client, sample_resources):
        """Test retry logic integration."""
        resource = sample_resources[0]

        # Mock transient failure followed by success
        responses = [
            Exception("Network timeout"),
            Exception("Connection refused"),
            ResourceSubmissionResponse(
                success=True,
                entity_id=uuid4(),
                status="PENDING",
            ),
        ]

        call_count = 0

        async def mock_submit(res):
            nonlocal call_count
            response = responses[call_count]
            call_count += 1
            if isinstance(response, Exception):
                raise response
            return response

        mock_client.submit_resource.side_effect = mock_submit

        # This would normally be handled by the retry decorator in the real client
        # For this test, we'll simulate the retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                result = await mock_client.submit_resource(resource)
                assert result.success is True
                break
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                await asyncio.sleep(0.1)  # Brief delay between retries

    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self):
        """Test rate limiting integration."""
        from arep.api.rate_limiter import create_rate_limiter

        # Create a rate limiter
        rate_limiter = create_rate_limiter(
            requests_per_second=2.0,
            burst_size=3,
        )

        # Test burst allowance
        start_time = asyncio.get_event_loop().time()

        # First 3 requests should be immediate (burst)
        for i in range(3):
            wait_time = await rate_limiter.acquire()
            assert wait_time < 0.1  # Should be nearly immediate

        # Next request should be rate limited
        wait_time = await rate_limiter.acquire()
        assert wait_time > 0.4  # Should wait for rate limit

        total_time = asyncio.get_event_loop().time() - start_time
        assert total_time > 0.4  # Total time should reflect rate limiting

    @pytest.mark.asyncio
    async def test_full_workflow_integration(self, sample_resources):
        """Test the complete end-to-end workflow."""
        # This test simulates a real workflow but with mocked external dependencies

        # 1. Initialize client (mocked)
        with patch("arep.api.client.AINavigatorClient") as MockClient:
            mock_client_instance = AsyncMock()
            MockClient.return_value = mock_client_instance

            # 2. Configure successful responses
            entity_ids = [uuid4() for _ in sample_resources]
            mock_responses = [
                ResourceSubmissionResponse(
                    success=True,
                    entity_id=entity_id,
                    status="PENDING",
                )
                for entity_id in entity_ids
            ]
            mock_client_instance.submit_resource.side_effect = mock_responses

            # 3. Create batch processor
            config = BatchConfig(
                batch_size=2,
                strategy=BatchStrategy.ADAPTIVE,
                validate_before_submit=True,
            )

            client = MockClient("https://api.example.com", "test-token")
            processor = BatchProcessor(client, config)

            # 4. Process resources
            result = await processor.process_resources(sample_resources)

            # 5. Verify complete workflow
            assert result.success is True
            assert result.total_submitted == len(sample_resources)
            assert result.successful_submissions == len(sample_resources)
            assert result.failed_submissions == 0

            # 6. Verify all resources were processed
            assert mock_client_instance.submit_resource.call_count == len(
                sample_resources
            )

            # 7. Check that validation was performed
            for call_args in mock_client_instance.submit_resource.call_args_list:
                resource = call_args[0][0]
                validation_errors = ResponseValidator.validate_resource_data(resource)
                assert len(validation_errors) == 0


@pytest.mark.asyncio
async def test_concurrent_submissions():
    """Test handling of concurrent submissions."""
    # Create multiple resources
    resources = [
        Resource(
            name=f"Concurrent Tool {i}",
            website_url=f"https://concurrent{i}.com",
            entity_type_id=uuid4(),
            short_description=f"Concurrent test tool {i}",
        )
        for i in range(10)
    ]

    # Mock client with delayed responses
    async def mock_submit(resource):
        await asyncio.sleep(0.1)  # Simulate network delay
        return ResourceSubmissionResponse(
            success=True,
            entity_id=uuid4(),
            status="PENDING",
        )

    # Submit all resources concurrently
    tasks = [mock_submit(resource) for resource in resources]
    results = await asyncio.gather(*tasks)

    # Verify all submissions succeeded
    assert len(results) == len(resources)
    assert all(result.success for result in results)


def test_configuration_integration():
    """Test configuration integration across modules."""
    from arep.config import AI_NAV_API_URL, AI_NAV_AUTH_TOKEN

    # Test that configuration is loaded
    assert AI_NAV_API_URL is not None
    # Note: AUTH_TOKEN might be None in test environment

    # Test client initialization with config
    if AI_NAV_AUTH_TOKEN:
        client = AINavigatorClient(
            api_url=AI_NAV_API_URL,
            auth_token=AI_NAV_AUTH_TOKEN,
        )
        assert client.api_url == AI_NAV_API_URL
        assert client.auth_token == AI_NAV_AUTH_TOKEN


def test_logging_integration():
    """Test logging integration across modules."""
    from arep.utils.logger import get_logger

    # Test that loggers can be created
    client_logger = get_logger("arep.api.client")
    validation_logger = get_logger("arep.api.validation")
    batch_logger = get_logger("arep.api.batch")

    assert client_logger is not None
    assert validation_logger is not None
    assert batch_logger is not None

    # Test that loggers have proper configuration
    assert client_logger.level <= 20  # INFO level or lower
    assert len(client_logger.handlers) > 0
