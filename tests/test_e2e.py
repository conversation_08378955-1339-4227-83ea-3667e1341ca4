"""
End-to-end tests for the AI Resource Enhancement Pipeline.
Tests the complete pipeline flow with mocked external services.
"""

import pytest
from datetime import datetime
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID

from arep.pipeline import EnhancementPipeline
from arep.models import <PERSON>malEntity
from arep.api.models import ResourceSubmissionResponse


@pytest.fixture
def sample_entities():
    """Create sample entities for E2E testing."""
    return [
        MinimalEntity(
            name="ChatGPT",
            url="https://chat.openai.com",
            logo_url="https://chat.openai.com/logo.png",
            source="test_scraper",
            discovered_at=datetime.now()
        ),
        MinimalEntity(
            name="Coursera AI Course",
            url="https://coursera.org/ai-course",
            logo_url="https://coursera.org/logo.png",
            source="test_scraper",
            discovered_at=datetime.now()
        ),
        MinimalEntity(
            name="Unsupported Entity",
            url="https://unsupported.com",
            source="test_scraper",
            discovered_at=datetime.now()
        )
    ]


@pytest.fixture
def mock_html_responses():
    """Mock HTML responses for different entity types."""
    return {
        "https://chat.openai.com": """
        <html>
        <head>
            <title>ChatGPT</title>
            <meta name="description" content="AI-powered conversational assistant">
            <meta property="og:description" content="Chat with AI">
        </head>
        <body>
            <h1>ChatGPT</h1>
            <p>Features include natural language processing, API access, and real-time responses.</p>
            <div class="pricing">Free tier available with premium subscriptions</div>
        </body>
        </html>
        """,
        "https://coursera.org/ai-course": """
        <html>
        <head>
            <title>AI Course</title>
            <meta name="description" content="Comprehensive AI course taught by experts">
        </head>
        <body>
            <h1>Machine Learning Course</h1>
            <p>This 10-week course is taught by Dr. Andrew Ng and covers fundamentals of AI.</p>
            <p>Prerequisites: Basic programming knowledge</p>
            <p>Certificate available upon completion</p>
            <p>Over 100,000 students enrolled</p>
        </body>
        </html>
        """,
        "https://unsupported.com": """
        <html>
        <head><title>Unsupported</title></head>
        <body><p>Some content</p></body>
        </html>
        """
    }


class TestEndToEndPipeline:
    """End-to-end test cases for the complete pipeline."""
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_flow(self, sample_entities, mock_html_responses):
        """Test the complete pipeline flow from collection to submission."""
        
        # Create pipeline with test configuration
        config = {
            'max_concurrent_entities': 2,
            'skip_failed_classification': True,
            'skip_unsupported_types': True
        }
        pipeline = EnhancementPipeline(config=config)
        
        # Mock collector to return our sample entities
        pipeline.collector.collect = AsyncMock(return_value=sample_entities)
        
        # Mock classifier responses
        async def mock_classify(entity):
            if "ChatGPT" in entity.name:
                from arep.models import ClassificationResult
                return ClassificationResult(
                    entity_type="tool",
                    entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
                    confidence=0.95,
                    reasoning="AI tool indicators detected"
                )
            elif "Course" in entity.name:
                from arep.models import ClassificationResult
                return ClassificationResult(
                    entity_type="course",
                    entity_type_id=UUID("b2c3d4e5-f6a7-4901-bcde-************"),
                    confidence=0.90,
                    reasoning="Educational content detected"
                )
            else:
                from arep.models import ClassificationResult
                return ClassificationResult(
                    entity_type="unknown",
                    entity_type_id=UUID("c3d4e5f6-a7b8-4012-cdef-************"),
                    confidence=0.60,
                    reasoning="Unclear entity type"
                )
        
        pipeline.classifier.classify = AsyncMock(side_effect=mock_classify)
        
        # Mock research engine
        async def mock_research_website(url, name):
            html_content = mock_html_responses.get(str(url), "<html><body>Default content</body></html>")
            
            # Simulate content extraction
            if "ChatGPT" in name:
                return {
                    'description': 'AI-powered conversational assistant',
                    'short_description': 'Chat with AI',
                    'features': ['Natural language processing', 'API access', 'Real-time responses'],
                    'pricing_info': 'Freemium',
                    'social_links': {}
                }
            elif "Course" in name:
                return {
                    'description': 'Comprehensive AI course taught by experts',
                    'short_description': '10-week AI course',
                    'features': ['Expert instruction', 'Certificate', 'Hands-on projects'],
                    'pricing_info': 'Paid',
                    'technical_details': {
                        'instructor': 'Dr. Andrew Ng',
                        'duration': '10 weeks',
                        'prerequisites': 'Basic programming knowledge',
                        'certificate': True,
                        'enrollment': 100000
                    }
                }
            else:
                return {
                    'description': 'Unknown entity',
                    'short_description': 'Unknown',
                    'features': []
                }
        
        # Mock the research engine methods
        pipeline.research_engine.__aenter__ = AsyncMock(return_value=pipeline.research_engine)
        pipeline.research_engine.__aexit__ = AsyncMock(return_value=None)
        pipeline.research_engine._research_website = AsyncMock(side_effect=mock_research_website)
        pipeline.research_engine._research_search_engines = AsyncMock(return_value={
            'additional_features': ['Advanced capabilities'],
            'market_position': 'Leading solution'
        })
        
        # Mock enhancer registry
        with patch('arep.enhancement.registry.enhancer_registry') as mock_registry:
            # Mock tool enhancer
            mock_tool_enhancer = AsyncMock()
            mock_tool_enhancer.enhance = AsyncMock(return_value=MagicMock(
                name="ChatGPT",
                website_url="https://chat.openai.com",
                entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
                tool_details=MagicMock(
                    key_features=["Natural language processing", "API access"],
                    has_api=True,
                    has_free_tier=True
                )
            ))
            
            # Mock course enhancer
            mock_course_enhancer = AsyncMock()
            mock_course_enhancer.enhance = AsyncMock(return_value=MagicMock(
                name="Coursera AI Course",
                website_url="https://coursera.org/ai-course",
                entity_type_id=UUID("b2c3d4e5-f6a7-4901-bcde-************"),
                course_details=MagicMock(
                    instructor_name="Dr. Andrew Ng",
                    duration_text="10 weeks",
                    skill_level="Beginner",
                    certificate_available=True
                )
            ))
            
            def mock_get_enhancer(entity_type):
                if entity_type == "tool":
                    return mock_tool_enhancer
                elif entity_type == "course":
                    return mock_course_enhancer
                else:
                    return None
            
            def mock_has_enhancer(entity_type):
                return entity_type in ["tool", "course"]
            
            mock_registry.get_enhancer.side_effect = mock_get_enhancer
            mock_registry.has_enhancer.side_effect = mock_has_enhancer
            
            # Mock API client
            pipeline.api_client.__aenter__ = AsyncMock(return_value=pipeline.api_client)
            pipeline.api_client.__aexit__ = AsyncMock(return_value=None)
            pipeline.api_client.submit_resource = AsyncMock(
                return_value=ResourceSubmissionResponse(
                    success=True,
                    message="Resource submitted successfully",
                    entity_id="a1b2c3d4-e5f6-7890-abcd-123456789012",
                    status="PENDING"
                )
            )
            
            # Run the complete pipeline
            results = await pipeline.run()
            
            # Verify results
            assert results['pipeline_completed'] is True
            assert results['entities_processed'] == 2  # ChatGPT and Course (unsupported skipped)
            assert results['entities_successful'] == 2
            assert results['entities_failed'] == 1  # Unsupported entity
            assert results['success_rate'] == 2/3  # 2 successful out of 3 total
            
            # Verify individual results
            processed_results = results['results']
            assert len(processed_results) == 2
            
            # Find ChatGPT result
            chatgpt_result = next(r for r in processed_results if "ChatGPT" in r['entity_name'])
            assert chatgpt_result['entity_type'] == 'tool'
            assert chatgpt_result['classification_confidence'] == 0.95
            
            # Find Course result
            course_result = next(r for r in processed_results if "Course" in r['entity_name'])
            assert course_result['entity_type'] == 'course'
            assert course_result['classification_confidence'] == 0.90
            
            # Verify API calls were made
            assert pipeline.api_client.submit_resource.call_count == 2
    
    @pytest.mark.asyncio
    async def test_pipeline_with_failures(self, sample_entities):
        """Test pipeline behavior with various failure scenarios."""
        
        config = {
            'max_concurrent_entities': 1,
            'skip_failed_classification': False,  # Don't skip failures
            'skip_unsupported_types': False
        }
        pipeline = EnhancementPipeline(config=config)
        
        # Mock collector
        pipeline.collector.collect = AsyncMock(return_value=sample_entities[:1])  # Just one entity
        
        # Mock classifier to fail
        pipeline.classifier.classify = AsyncMock(side_effect=Exception("Classification failed"))
        
        # Run pipeline
        results = await pipeline.run()
        
        # Should have failures
        assert results['entities_processed'] == 0
        assert results['entities_failed'] == 1
        assert results['success_rate'] == 0.0
    
    @pytest.mark.asyncio
    async def test_pipeline_dry_run_mode(self, sample_entities):
        """Test pipeline in dry-run mode."""
        
        config = {'dry_run': True}
        
        # Import and test the runner
        from run import PipelineRunner
        
        runner = PipelineRunner()
        runner.config.update(config)
        
        # Mock the pipeline components
        with patch.object(runner, 'pipeline') as mock_pipeline:
            mock_pipeline.collector.collect = AsyncMock(return_value=sample_entities)
            mock_pipeline._classify_entity = AsyncMock(return_value=MagicMock(
                entity_type="tool",
                classification_confidence=0.95
            ))
            mock_pipeline._research_entity = AsyncMock(return_value=MagicMock(
                features=["Feature 1", "Feature 2"],
                description="Test description"
            ))
            
            # Run dry run
            results = await runner._run_dry_run()
            
            # Verify dry run results
            assert results['dry_run'] is True
            assert results['total_entities'] == 3
            assert results['sample_processed'] <= 3
    
    @pytest.mark.asyncio
    async def test_concurrent_processing_limits(self, sample_entities):
        """Test that concurrent processing respects limits."""
        
        config = {'max_concurrent_entities': 1}  # Force sequential processing
        pipeline = EnhancementPipeline(config=config)
        
        # Track processing order
        processing_order = []
        
        async def track_processing(entity):
            processing_order.append(entity.name)
            # Simulate some processing time
            import asyncio
            await asyncio.sleep(0.01)
            return None  # Return None to simulate failure/skip
        
        # Mock the process_single_entity method
        pipeline.process_single_entity = AsyncMock(side_effect=track_processing)
        
        # Process entities
        await pipeline._process_entities_concurrent(sample_entities)
        
        # Verify all entities were processed
        assert len(processing_order) == 3
        assert "ChatGPT" in processing_order
        assert "Coursera AI Course" in processing_order
        assert "Unsupported Entity" in processing_order
