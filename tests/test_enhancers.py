"""
Tests for entity enhancers in the AI Resource Enhancement Pipeline.
"""

import pytest
from datetime import datetime
from uuid import UUID

from arep.enhancement.tool_enhancer import ToolEnhancer
from arep.enhancement.course_enhancer import CourseEnhancer
from arep.enhancement.base import EnhancementError
from arep.models import ClassifiedEntity, ResearchData
from arep.api.models import ToolDetails, CourseDetails


@pytest.fixture
def sample_classified_entity():
    """Create a sample classified entity."""
    return ClassifiedEntity(
        name="Test AI Tool",
        url="https://testaitool.com",
        logo_url="https://testaitool.com/logo.png",
        source="test_scraper",
        discovered_at=datetime.now(),
        entity_type="tool",
        entity_type_id=UUID("a1b2c3d4-e5f6-7890-abcd-123456789012"),
        classification_confidence=0.95,
        classification_reasoning="Strong AI tool indicators",
        alternative_types=["software"]
    )


@pytest.fixture
def sample_course_entity():
    """Create a sample classified course entity."""
    return ClassifiedEntity(
        name="AI Fundamentals Course",
        url="https://aicourse.com",
        logo_url="https://aicourse.com/logo.png",
        source="test_scraper",
        discovered_at=datetime.now(),
        entity_type="course",
        entity_type_id=UUID("b2c3d4e5-f6a7-4901-bcde-************"),
        classification_confidence=0.90,
        classification_reasoning="Educational content indicators",
        alternative_types=["content"]
    )


@pytest.fixture
def tool_research_data():
    """Create research data for a tool."""
    return ResearchData(
        description="A comprehensive AI tool for machine learning automation and data analysis with advanced features",
        short_description="AI automation and analysis tool",
        features=[
            "Machine Learning Automation",
            "Real-time Data Analysis", 
            "API Integration",
            "Custom Workflows",
            "Cloud Deployment"
        ],
        categories=["AI Tools", "Automation", "Analytics"],
        tags=["ai", "automation", "analytics", "machine-learning"],
        pricing_info="Freemium with premium tiers",
        contact_info="<EMAIL>",
        social_links={
            "twitter": "https://twitter.com/testaitool",
            "github": "https://github.com/testaitool"
        },
        technical_details={
            "api": True,
            "platforms": ["web", "mobile", "desktop"],
            "integrations": ["slack", "zapier", "salesforce"]
        },
        research_sources=["https://testaitool.com"],
        research_timestamp=datetime.now()
    )


@pytest.fixture
def course_research_data():
    """Create research data for a course."""
    return ResearchData(
        description="A comprehensive 8-week course on AI fundamentals taught by Dr. Jane Smith. Covers machine learning basics, neural networks, and practical applications. Suitable for beginners with basic programming knowledge.",
        short_description="8-week AI fundamentals course for beginners",
        features=[
            "Interactive lessons",
            "Hands-on projects",
            "Certificate of completion",
            "Community support",
            "Lifetime access"
        ],
        categories=["Education", "AI", "Online Learning"],
        tags=["ai", "course", "beginner", "certificate"],
        pricing_info="$299 with certificate, free audit option",
        contact_info="<EMAIL>",
        social_links={
            "twitter": "https://twitter.com/aicourse",
            "linkedin": "https://linkedin.com/company/aicourse"
        },
        technical_details={
            "duration": "8 weeks",
            "instructor": "Dr. Jane Smith",
            "prerequisites": "Basic programming knowledge",
            "certificate": True,
            "enrollment": 15000
        },
        research_sources=["https://aicourse.com"],
        research_timestamp=datetime.now()
    )


class TestToolEnhancer:
    """Test cases for the ToolEnhancer."""
    
    @pytest.mark.asyncio
    async def test_tool_enhancement_success(self, sample_classified_entity, tool_research_data):
        """Test successful tool enhancement."""
        enhancer = ToolEnhancer()
        
        resource = await enhancer.enhance(sample_classified_entity, tool_research_data)
        
        # Verify base fields
        assert resource.name == "Test AI Tool"
        assert str(resource.website_url) == "https://testaitool.com"
        assert resource.entity_type_id == UUID("a1b2c3d4-e5f6-7890-abcd-123456789012")
        assert resource.short_description == "AI automation and analysis tool"
        
        # Verify tool-specific details
        assert resource.tool_details is not None
        tool_details = resource.tool_details
        
        assert len(tool_details.key_features) > 0
        assert "Machine Learning Automation" in tool_details.key_features
        assert tool_details.has_api is True
        assert tool_details.has_free_tier is True
        assert len(tool_details.use_cases) > 0
        assert len(tool_details.supported_platforms) > 0
    
    @pytest.mark.asyncio
    async def test_tool_enhancement_minimal_data(self, sample_classified_entity):
        """Test tool enhancement with minimal research data."""
        minimal_research_data = ResearchData(
            short_description="Basic AI tool",
            research_sources=["https://testaitool.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = ToolEnhancer()
        resource = await enhancer.enhance(sample_classified_entity, minimal_research_data)
        
        # Should still create valid resource with defaults
        assert resource.tool_details is not None
        assert len(resource.tool_details.key_features) >= 2  # Default features
        assert resource.tool_details.has_api is False  # No API detected
        assert resource.tool_details.has_free_tier is False  # No pricing info
    
    @pytest.mark.asyncio
    async def test_api_detection(self, sample_classified_entity):
        """Test API availability detection."""
        # Test with API indicators in features
        research_data_with_api = ResearchData(
            features=["REST API", "GraphQL endpoint", "Webhook support"],
            research_sources=["https://testaitool.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = ToolEnhancer()
        resource = await enhancer.enhance(sample_classified_entity, research_data_with_api)
        
        assert resource.tool_details.has_api is True
    
    @pytest.mark.asyncio
    async def test_free_tier_detection(self, sample_classified_entity):
        """Test free tier detection."""
        # Test freemium pricing
        research_data_freemium = ResearchData(
            pricing_info="Freemium model with premium features",
            research_sources=["https://testaitool.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = ToolEnhancer()
        resource = await enhancer.enhance(sample_classified_entity, research_data_freemium)
        
        assert resource.tool_details.has_free_tier is True
        
        # Test paid-only pricing
        research_data_paid = ResearchData(
            pricing_info="Subscription-based pricing starting at $29/month",
            research_sources=["https://testaitool.com"],
            research_timestamp=datetime.now()
        )
        
        resource_paid = await enhancer.enhance(sample_classified_entity, research_data_paid)
        assert resource_paid.tool_details.has_free_tier is False
    
    @pytest.mark.asyncio
    async def test_feature_extraction_from_text(self, sample_classified_entity):
        """Test feature extraction from descriptive text."""
        research_data_text_features = ResearchData(
            description="This tool offers advanced machine learning capabilities, supports real-time data processing, and enables seamless API integration for developers.",
            research_sources=["https://testaitool.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = ToolEnhancer()
        resource = await enhancer.enhance(sample_classified_entity, research_data_text_features)
        
        features = resource.tool_details.key_features
        assert len(features) > 0
        # Should extract features from the description


class TestCourseEnhancer:
    """Test cases for the CourseEnhancer."""
    
    @pytest.mark.asyncio
    async def test_course_enhancement_success(self, sample_course_entity, course_research_data):
        """Test successful course enhancement."""
        enhancer = CourseEnhancer()
        
        resource = await enhancer.enhance(sample_course_entity, course_research_data)
        
        # Verify base fields
        assert resource.name == "AI Fundamentals Course"
        assert str(resource.website_url) == "https://aicourse.com"
        assert resource.short_description == "8-week AI fundamentals course for beginners"
        
        # Verify course-specific details
        assert resource.course_details is not None
        course_details = resource.course_details
        
        assert course_details.instructor_name == "Dr. Jane Smith"
        assert course_details.duration_text == "8 weeks"
        assert course_details.skill_level == "Beginner"
        assert course_details.prerequisites == "Basic programming knowledge"
        assert course_details.certificate_available is True
        assert course_details.enrollment_count == 15000
    
    @pytest.mark.asyncio
    async def test_instructor_name_extraction(self, sample_course_entity):
        """Test instructor name extraction from description."""
        research_data_instructor = ResearchData(
            description="This course is taught by Prof. John Doe, a leading expert in artificial intelligence.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = CourseEnhancer()
        resource = await enhancer.enhance(sample_course_entity, research_data_instructor)
        
        assert resource.course_details.instructor_name == "Prof. John Doe"
    
    @pytest.mark.asyncio
    async def test_duration_extraction(self, sample_course_entity):
        """Test duration extraction from description."""
        research_data_duration = ResearchData(
            description="This is a comprehensive 12-week program covering all aspects of machine learning.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = CourseEnhancer()
        resource = await enhancer.enhance(sample_course_entity, research_data_duration)
        
        assert resource.course_details.duration_text == "12 weeks"
    
    @pytest.mark.asyncio
    async def test_skill_level_determination(self, sample_course_entity):
        """Test skill level determination."""
        # Test advanced level
        research_data_advanced = ResearchData(
            description="This advanced course requires extensive experience in machine learning and deep understanding of neural networks.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = CourseEnhancer()
        resource = await enhancer.enhance(sample_course_entity, research_data_advanced)
        
        assert resource.course_details.skill_level == "Advanced"
        
        # Test intermediate level
        research_data_intermediate = ResearchData(
            description="This intermediate course assumes some experience with programming and basic statistics.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        resource_intermediate = await enhancer.enhance(sample_course_entity, research_data_intermediate)
        assert resource_intermediate.course_details.skill_level == "Intermediate"
    
    @pytest.mark.asyncio
    async def test_certificate_detection(self, sample_course_entity):
        """Test certificate availability detection."""
        # Test with certificate
        research_data_cert = ResearchData(
            description="Upon completion, students receive a certificate of achievement.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = CourseEnhancer()
        resource = await enhancer.enhance(sample_course_entity, research_data_cert)
        
        assert resource.course_details.certificate_available is True
        
        # Test without certificate
        research_data_no_cert = ResearchData(
            description="This course provides knowledge but no certificate is awarded.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        resource_no_cert = await enhancer.enhance(sample_course_entity, research_data_no_cert)
        assert resource_no_cert.course_details.certificate_available is False
    
    @pytest.mark.asyncio
    async def test_enrollment_count_extraction(self, sample_course_entity):
        """Test enrollment count extraction."""
        research_data_enrollment = ResearchData(
            description="Join over 25,000 students who have already enrolled in this popular course.",
            research_sources=["https://aicourse.com"],
            research_timestamp=datetime.now()
        )
        
        enhancer = CourseEnhancer()
        resource = await enhancer.enhance(sample_course_entity, research_data_enrollment)
        
        assert resource.course_details.enrollment_count == 25000
