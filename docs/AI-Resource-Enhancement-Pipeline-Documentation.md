# AI Resource Enhancement Pipeline - Complete Documentation

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Components](#components)
4. [Installation & Setup](#installation--setup)
5. [Usage Guide](#usage-guide)
6. [API Reference](#api-reference)
7. [Configuration](#configuration)
8. [Testing](#testing)
9. [Deployment](#deployment)
10. [Troubleshooting](#troubleshooting)
11. [Contributing](#contributing)

## Overview

The AI Resource Enhancement Pipeline is a comprehensive system designed to automatically discover, classify, research, enhance, and submit AI-related resources to the AI Navigator platform. The pipeline transforms minimal entity data into rich, structured information suitable for the AI Navigator API.

### Key Features

- **Automated Discovery**: Collect entities from multiple sources
- **Intelligent Classification**: Determine entity types with confidence scoring
- **Comprehensive Research**: Gather detailed information from web sources
- **Type-Specific Enhancement**: Transform data using specialized enhancers
- **Robust API Integration**: Submit enhanced entities to AI Navigator
- **Production-Ready**: Concurrent processing, error handling, monitoring

### Supported Entity Types

The pipeline supports 22 different entity types:

| Category | Entity Types |
|----------|-------------|
| **Technology** | tool, software, model, platform, hardware |
| **Education** | course, book, research_paper |
| **Business** | agency, service_provider, investor |
| **Community** | content_creator, community, newsletter, podcast |
| **Data** | dataset |
| **Projects** | project_reference |
| **Opportunities** | event, job, grant, bounty |
| **Media** | news |

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Data Sources  │───▶│   Collection     │───▶│  Classification │
│                 │    │   Pipeline       │    │   Engine        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│  AI Navigator   │◀───│   Enhancement    │◀───│   Research      │
│     API         │    │   Pipeline       │    │   Engine        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Pipeline Flow

1. **Collection**: Discover and collect minimal entity data
2. **Classification**: Determine entity type and confidence
3. **Research**: Gather comprehensive data about entities
4. **Enhancement**: Transform data into API-ready format
5. **Submission**: Submit to AI Navigator API

### Core Components

```
arep/
├── collectors/          # Data collection from various sources
├── classification/      # Entity type classification
├── enhancement/         # Research and data enhancement
├── api/                # AI Navigator API integration
├── monitoring/         # Metrics and monitoring
├── utils/              # Shared utilities
└── models.py           # Core data models
```

## Components

### 1. Collection System (`arep/collectors/`)

#### Base Scraper (`arep/collectors/base.py`)
- Abstract base class for all scrapers
- Built-in rate limiting and retry logic
- Session management and metrics tracking
- Consistent error handling

#### Data Collector (`arep/collectors/collector.py`)
- Orchestrates multiple scrapers concurrently
- Deduplication and data validation
- Comprehensive metrics collection
- Dynamic scraper management

**Key Features:**
- Concurrent scraping with configurable limits
- Automatic retry with exponential backoff
- Rate limiting to respect website policies
- Comprehensive error handling and logging

### 2. Classification System (`arep/classification/`)

#### Entity Type Classifier (`arep/classification/classifier.py`)
- Multi-strategy classification approach
- URL pattern analysis
- Name-based classification
- Content analysis integration
- Weighted voting system

#### Classification Strategies:
1. **URL Pattern Classifier**: Analyzes URL structure and domain patterns
2. **Name-Based Classifier**: Uses keyword matching and NLP techniques
3. **Content Classifier**: Analyzes webpage content and structure
4. **LLM Classifier**: Uses OpenAI API for complex cases

#### Confidence Scoring (`arep/classification/confidence.py`)
- Multi-factor confidence calculation
- Classifier agreement analysis
- Content quality assessment
- URL reliability scoring

### 3. Enhancement System (`arep/enhancement/`)

#### Smart Research Engine (`arep/enhancement/research.py`)

**Core Capabilities:**
- Web content analysis with BeautifulSoup
- Meta tag extraction (title, description, keywords)
- Social media link detection
- Pricing information analysis
- Contact information extraction
- Feature list extraction

**Research Sources:**
- Primary: Entity's official website
- Secondary: Search engine results (extensible)
- Future: Social media APIs, review platforms

**Data Extraction:**
```python
research_data = {
    'description': 'Comprehensive description',
    'short_description': 'Brief summary',
    'features': ['Feature 1', 'Feature 2'],
    'categories': ['AI Tools', 'Automation'],
    'tags': ['ai', 'automation', 'ml'],
    'pricing_info': 'Freemium',
    'contact_info': '<EMAIL>',
    'social_links': {'twitter': 'https://twitter.com/...'},
    'technical_details': {...}
}
```

#### Base Enhancer (`arep/enhancement/base.py`)

**Abstract Interface:**
```python
class BaseEnhancer(ABC):
    @abstractmethod
    async def enhance(self, entity: ClassifiedEntity, research_data: ResearchData) -> Resource:
        pass
```

**Common Utilities:**
- Base field extraction
- Text cleaning and validation
- Feature list processing
- Pricing tier determination

#### Type-Specific Enhancers

##### Tool Enhancer (`arep/enhancement/tool_enhancer.py`)
Transforms research data for AI tools and software:

**Capabilities:**
- API availability detection
- Free tier analysis
- Use case identification
- Integration capabilities
- Platform support detection
- Deployment options analysis

**Output Structure:**
```python
ToolDetails(
    key_features=['ML Automation', 'API Integration'],
    has_api=True,
    has_free_tier=True,
    use_cases=['automation', 'analytics'],
    integrations_available=['slack', 'zapier'],
    supported_platforms=['web', 'mobile'],
    deployment_options=['cloud', 'on-premise']
)
```

##### Course Enhancer (`arep/enhancement/course_enhancer.py`)
Specialized for educational content:

**Capabilities:**
- Instructor name extraction
- Duration analysis
- Skill level determination
- Prerequisites identification
- Certificate availability detection
- Enrollment count extraction

**Output Structure:**
```python
CourseDetails(
    instructor_name='Dr. Jane Smith',
    duration_text='8 weeks',
    skill_level='Beginner',
    prerequisites='Basic programming knowledge',
    certificate_available=True,
    enrollment_count=15000
)
```

#### Enhancer Registry (`arep/enhancement/registry.py`)
- Centralized enhancer management
- Dynamic registration system
- Type-safe enhancer retrieval
- Extensible architecture

### 4. API Integration (`arep/api/`)

#### AI Navigator Client (`arep/api/client.py`)
- Robust HTTP client with authentication
- Automatic retry with exponential backoff
- Rate limiting and request queuing
- Comprehensive error handling

**Features:**
- Resource submission with validation
- Batch processing capabilities
- Health check monitoring
- Detailed response handling

#### Data Models (`arep/api/models.py`)
Complete Pydantic models for all 22 entity types:
- Type-specific detail models
- Validation and serialization
- API-compatible structure
- Comprehensive field coverage

### 5. Pipeline Orchestration (`arep/pipeline.py`)

#### Enhancement Pipeline
Main orchestrator managing the complete process:

**Configuration Options:**
```python
config = {
    'max_concurrent_entities': 5,
    'skip_failed_classification': True,
    'skip_unsupported_types': True,
    'timeout_seconds': 30
}
```

**Processing Flow:**
1. Entity collection from all configured sources
2. Concurrent classification with confidence scoring
3. Comprehensive research and data gathering
4. Type-specific enhancement and validation
5. API submission with retry logic

**Status Tracking:**
- Real-time processing status for each entity
- Comprehensive error reporting
- Performance metrics collection
- Success/failure rate monitoring

### 6. Monitoring & Metrics (`arep/monitoring/`)

#### Metrics Collection (`arep/monitoring/metrics.py`)
- Processing time tracking
- Success/failure rates
- API response monitoring
- Resource utilization metrics

**Key Metrics:**
- Entities processed per minute
- Classification accuracy
- Research success rate
- API submission success rate
- Average processing time per entity

## Installation & Setup

### Prerequisites
- Python 3.9+
- pip package manager
- Virtual environment (recommended)

### Installation Steps

1. **Clone the Repository**
```bash
git clone <repository-url>
cd AI-nav_scrape_2
```

2. **Create Virtual Environment**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install Dependencies**
```bash
pip install -r requirements.txt
```

4. **Environment Configuration**
Create `.env` file:
```env
AI_NAV_API_URL=https://ai-nav.onrender.com
AI_NAV_AUTH_TOKEN=your_auth_token_here
OPENAI_API_KEY=your_openai_key_here  # Optional for LLM classification
```

5. **Verify Installation**
```bash
python -m pytest tests/ -v
```

## Usage Guide

### Basic Usage

#### Command Line Interface

**Run Complete Pipeline:**
```bash
python run.py
```

**Dry Run Mode (No API Submissions):**
```bash
python run.py --dry-run
```

**Custom Configuration:**
```bash
python run.py --config config.json --log-level DEBUG
```

**Save Results:**
```bash
python run.py --output results.json
```

#### Programmatic Usage

**Basic Pipeline Execution:**
```python
import asyncio
from arep.pipeline import EnhancementPipeline

async def main():
    config = {
        'max_concurrent_entities': 3,
        'skip_failed_classification': True
    }
    
    pipeline = EnhancementPipeline(config=config)
    results = await pipeline.run()
    
    print(f"Processed: {results['entities_processed']}")
    print(f"Success rate: {results['success_rate']:.1%}")

asyncio.run(main())
```

**Single Entity Processing:**
```python
from arep.models import MinimalEntity
from datetime import datetime

# Create entity
entity = MinimalEntity(
    name="ChatGPT",
    url="https://chat.openai.com",
    source="manual",
    discovered_at=datetime.now()
)

# Process single entity
result = await pipeline.process_single_entity(entity)
```

### Advanced Usage

#### Custom Enhancer Development

**Create Custom Enhancer:**
```python
from arep.enhancement.base import BaseEnhancer
from arep.api.models import Resource

class CustomEnhancer(BaseEnhancer):
    def __init__(self):
        super().__init__("custom_type")
    
    async def enhance(self, entity, research_data):
        # Custom enhancement logic
        base_fields = self._extract_base_fields(entity, research_data)
        
        # Add custom processing
        custom_details = self._create_custom_details(research_data)
        
        return Resource(**base_fields, custom_details=custom_details)
```

**Register Custom Enhancer:**
```python
from arep.enhancement.registry import enhancer_registry

enhancer_registry.register_enhancer("custom_type", CustomEnhancer())
```

#### Custom Data Source Integration

**Create Custom Scraper:**
```python
from arep.collectors.base import BaseScraper

class CustomScraper(BaseScraper):
    def __init__(self):
        super().__init__("custom_source")
    
    async def scrape(self):
        # Custom scraping logic
        entities = []
        # ... collect entities
        return entities
```

**Add to Collector:**
```python
from arep.collectors.collector import MinimalDataCollector

collector = MinimalDataCollector()
collector.add_scraper(CustomScraper())
```

## API Reference

### Core Models

#### MinimalEntity
Basic entity data collected during discovery:
```python
class MinimalEntity(BaseModel):
    name: str                    # Entity name
    url: HttpUrl                 # Primary URL
    logo_url: Optional[HttpUrl]  # Logo URL
    source: str                  # Discovery source
    discovered_at: datetime      # Discovery timestamp
```

#### ClassifiedEntity
Entity with classification results:
```python
class ClassifiedEntity(MinimalEntity):
    entity_type: str                        # Determined type
    entity_type_id: UUID                    # Type UUID
    classification_confidence: float        # Confidence score (0-1)
    classification_reasoning: Optional[str] # Reasoning
    alternative_types: List[str]            # Alternative types
```

#### ResearchData
Comprehensive research results:
```python
class ResearchData(BaseModel):
    description: Optional[str]           # Full description
    short_description: Optional[str]     # Brief summary
    features: List[str]                  # Feature list
    categories: List[str]                # Categories
    tags: List[str]                      # Tags
    pricing_info: Optional[str]          # Pricing model
    contact_info: Optional[str]          # Contact information
    social_links: Optional[dict]         # Social media links
    technical_details: Optional[dict]    # Technical specifications
    research_sources: List[str]          # Source URLs
    research_timestamp: datetime         # Research time
```

### Pipeline API

#### EnhancementPipeline

**Constructor:**
```python
pipeline = EnhancementPipeline(config: Optional[Dict[str, Any]] = None)
```

**Configuration Options:**
```python
config = {
    'max_concurrent_entities': 5,        # Concurrent processing limit
    'skip_failed_classification': True,  # Skip classification failures
    'skip_unsupported_types': True,      # Skip unsupported types
    'timeout_seconds': 30,               # Request timeout
    'retry_attempts': 3,                 # Retry attempts
    'retry_delay': 1.0                   # Retry delay (seconds)
}
```

**Methods:**

**`async run() -> Dict[str, Any]`**
Execute the complete pipeline:
```python
results = await pipeline.run()
# Returns:
{
    'pipeline_completed': True,
    'total_processing_time': 45.2,
    'entities_processed': 10,
    'entities_successful': 8,
    'entities_failed': 2,
    'success_rate': 0.8,
    'results': [...],
    'processing_status': {...}
}
```

**`async process_single_entity(entity: MinimalEntity) -> Optional[Dict[str, Any]]`**
Process a single entity:
```python
result = await pipeline.process_single_entity(entity)
# Returns:
{
    'entity_id': 'uuid',
    'entity_name': 'ChatGPT',
    'entity_type': 'tool',
    'classification_confidence': 0.95,
    'submission_result': {...},
    'processing_time': 2.3
}
```

### Research Engine API

#### SmartResearchEngine

**Usage:**
```python
async with SmartResearchEngine() as research_engine:
    research_data = await research_engine.research(classified_entity)
```

**Methods:**

**`async research(entity: ClassifiedEntity) -> ResearchData`**
Conduct comprehensive research on an entity.

**Research Process:**
1. Website content analysis
2. Meta tag extraction
3. Social link detection
4. Pricing information analysis
5. Feature extraction
6. Search engine augmentation

### Enhancer API

#### BaseEnhancer

**Abstract Methods:**
```python
async def enhance(self, entity: ClassifiedEntity, research_data: ResearchData) -> Resource:
    """Transform entity and research data into API-ready Resource."""
    pass
```

**Utility Methods:**
```python
def _extract_base_fields(self, entity, research_data) -> Dict[str, Any]:
    """Extract common fields for all entity types."""

def _validate_required_fields(self, resource_data, required_fields) -> bool:
    """Validate required fields are present."""

def _clean_text_field(self, text, max_length=None) -> Optional[str]:
    """Clean and validate text fields."""

def _determine_pricing_tier(self, research_data) -> Optional[str]:
    """Determine pricing tier from research data."""
```

### API Client

#### AINavigatorClient

**Usage:**
```python
async with AINavigatorClient() as client:
    response = await client.submit_resource(resource)
```

**Methods:**

**`async submit_resource(resource: Resource) -> ResourceSubmissionResponse`**
Submit a single resource to the API.

**`async submit_batch(resources: List[Resource]) -> BatchSubmissionResponse`**
Submit multiple resources in a batch.

**`async health_check() -> bool`**
Check API health status.

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `AI_NAV_API_URL` | AI Navigator API base URL | Yes | - |
| `AI_NAV_AUTH_TOKEN` | Authentication token | Yes | - |
| `OPENAI_API_KEY` | OpenAI API key for LLM classification | No | - |
| `LOG_LEVEL` | Logging level | No | INFO |
| `MAX_CONCURRENT_REQUESTS` | Max concurrent API requests | No | 10 |
| `REQUEST_TIMEOUT` | Request timeout in seconds | No | 30 |

### Configuration File Format

**config.json:**
```json
{
  "pipeline": {
    "max_concurrent_entities": 5,
    "skip_failed_classification": true,
    "skip_unsupported_types": true,
    "timeout_seconds": 30
  },
  "research": {
    "enable_search_engines": false,
    "max_research_time": 60,
    "user_agent": "AI-Resource-Enhancement-Pipeline/1.0"
  },
  "api": {
    "max_retries": 3,
    "retry_delay": 1.0,
    "batch_size": 10
  },
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  }
}
```

### Rate Limiting Configuration

**Default Rate Limits:**
- API requests: 10 requests/second
- Web scraping: 1 request/second per domain
- Concurrent entities: 5 entities simultaneously

**Custom Rate Limiting:**
```python
from arep.api.rate_limiter import RateLimiter

rate_limiter = RateLimiter(
    requests_per_second=5,
    burst_size=10,
    window_size=60
)
```

## Testing

### Test Structure

```
tests/
├── test_api_client.py          # API client tests
├── test_classification.py      # Classification system tests
├── test_collection_pipeline.py # Collection pipeline tests
├── test_e2e.py                # End-to-end integration tests
├── test_enhancers.py          # Enhancer tests
├── test_integration.py        # Integration tests
├── test_pipeline.py           # Main pipeline tests
├── test_research_engine.py    # Research engine tests
└── test_validation.py         # Validation tests
```

### Running Tests

**Run All Tests:**
```bash
python -m pytest tests/ -v
```

**Run Specific Test Categories:**
```bash
# Unit tests only
python -m pytest tests/test_enhancers.py -v

# Integration tests
python -m pytest tests/test_integration.py -v

# End-to-end tests
python -m pytest tests/test_e2e.py -v
```

**Run with Coverage:**
```bash
python -m pytest tests/ --cov=arep --cov-report=html
```

### Test Configuration

**pytest.ini:**
```ini
[pytest]
asyncio_mode = auto
python_files = tests.py test_*.py *_test.py
testpaths = tests
addopts =
    --strict-markers
    --disable-warnings
    -ra
```

### Mock Testing

The test suite uses comprehensive mocking for external dependencies:

**API Mocking:**
```python
@patch('arep.api.client.AINavigatorClient')
async def test_pipeline_with_mock_api(mock_client):
    mock_client.submit_resource.return_value = {
        'success': True,
        'entity_id': 'test-uuid'
    }
```

**Web Scraping Mocking:**
```python
@patch('aiohttp.ClientSession.get')
async def test_research_with_mock_response(mock_get):
    mock_response = AsyncMock()
    mock_response.status = 200
    mock_response.text.return_value = "<html>...</html>"
    mock_get.return_value.__aenter__.return_value = mock_response
```

### Performance Testing

**Load Testing:**
```python
import asyncio
from arep.pipeline import EnhancementPipeline

async def load_test():
    pipeline = EnhancementPipeline({'max_concurrent_entities': 10})

    # Create test entities
    entities = [create_test_entity(i) for i in range(100)]

    start_time = time.time()
    results = await pipeline._process_entities_concurrent(entities)
    end_time = time.time()

    print(f"Processed {len(results)} entities in {end_time - start_time:.2f}s")
    print(f"Rate: {len(results) / (end_time - start_time):.2f} entities/second")
```

## Deployment

### Production Deployment

#### Docker Deployment

**Dockerfile:**
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["python", "run.py"]
```

**docker-compose.yml:**
```yaml
version: '3.8'
services:
  pipeline:
    build: .
    environment:
      - AI_NAV_API_URL=${AI_NAV_API_URL}
      - AI_NAV_AUTH_TOKEN=${AI_NAV_AUTH_TOKEN}
      - LOG_LEVEL=INFO
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    restart: unless-stopped
```

#### Kubernetes Deployment

**deployment.yaml:**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai-pipeline
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ai-pipeline
  template:
    metadata:
      labels:
        app: ai-pipeline
    spec:
      containers:
      - name: pipeline
        image: ai-pipeline:latest
        env:
        - name: AI_NAV_API_URL
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: url
        - name: AI_NAV_AUTH_TOKEN
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: token
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### Monitoring & Observability

#### Logging Configuration

**Production Logging:**
```python
import logging
from pythonjsonlogger import jsonlogger

# Configure structured logging
logHandler = logging.StreamHandler()
formatter = jsonlogger.JsonFormatter()
logHandler.setFormatter(formatter)
logger = logging.getLogger()
logger.addHandler(logHandler)
logger.setLevel(logging.INFO)
```

#### Metrics Collection

**Prometheus Metrics:**
```python
from prometheus_client import Counter, Histogram, Gauge

# Define metrics
entities_processed = Counter('entities_processed_total', 'Total entities processed')
processing_time = Histogram('entity_processing_seconds', 'Time spent processing entities')
active_entities = Gauge('entities_processing_active', 'Currently processing entities')
```

#### Health Checks

**Health Check Endpoint:**
```python
from fastapi import FastAPI

app = FastAPI()

@app.get("/health")
async def health_check():
    # Check API connectivity
    api_healthy = await check_api_health()

    # Check system resources
    memory_usage = get_memory_usage()

    return {
        "status": "healthy" if api_healthy else "unhealthy",
        "api_connection": api_healthy,
        "memory_usage": memory_usage,
        "timestamp": datetime.now().isoformat()
    }
```

### Scaling Considerations

#### Horizontal Scaling
- **Stateless Design**: Pipeline instances are stateless and can be scaled horizontally
- **Queue-Based Processing**: Use Redis or RabbitMQ for distributed task processing
- **Load Balancing**: Distribute entities across multiple pipeline instances

#### Vertical Scaling
- **Memory**: Increase for larger concurrent processing
- **CPU**: Scale for faster research and classification
- **Network**: Ensure sufficient bandwidth for API calls

#### Performance Optimization

**Concurrent Processing:**
```python
# Optimize for your infrastructure
config = {
    'max_concurrent_entities': 20,  # Increase for more powerful machines
    'api_batch_size': 50,           # Batch API submissions
    'research_timeout': 15,         # Reduce for faster processing
    'enable_caching': True          # Cache research results
}
```

**Caching Strategy:**
```python
import redis
from functools import wraps

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cache_research_results(func):
    @wraps(func)
    async def wrapper(self, entity):
        cache_key = f"research:{hash(entity.url)}"
        cached = redis_client.get(cache_key)

        if cached:
            return json.loads(cached)

        result = await func(self, entity)
        redis_client.setex(cache_key, 3600, json.dumps(result))
        return result

    return wrapper
```

## Troubleshooting

### Common Issues

#### 1. Classification Failures

**Symptoms:**
- High number of entities with "unknown" classification
- Low confidence scores across entities

**Solutions:**
```python
# Enable debug logging for classification
import logging
logging.getLogger('arep.classification').setLevel(logging.DEBUG)

# Check classification patterns
from arep.classification.classifier import EntityTypeClassifier
classifier = EntityTypeClassifier()
result = await classifier.classify(entity, debug=True)
print(result.reasoning)
```

**Troubleshooting Steps:**
1. Verify entity URLs are accessible
2. Check if entity types are supported
3. Review classification patterns and keywords
4. Consider enabling LLM classification for complex cases

#### 2. Research Engine Timeouts

**Symptoms:**
- Frequent timeout errors during research
- Slow pipeline processing

**Solutions:**
```python
# Adjust timeout settings
config = {
    'research_timeout': 60,  # Increase timeout
    'max_concurrent_entities': 3,  # Reduce concurrency
    'retry_attempts': 5  # Increase retries
}

# Enable request debugging
import aiohttp
import logging
logging.getLogger('aiohttp').setLevel(logging.DEBUG)
```

#### 3. API Submission Failures

**Symptoms:**
- 401 Unauthorized errors
- 429 Rate limit exceeded
- Validation errors

**Solutions:**

**Authentication Issues:**
```bash
# Verify token
curl -H "Authorization: Bearer $AI_NAV_AUTH_TOKEN" \
     https://ai-nav.onrender.com/api/health
```

**Rate Limiting:**
```python
# Reduce API request rate
from arep.api.rate_limiter import RateLimiter
rate_limiter = RateLimiter(requests_per_second=2)
```

**Validation Errors:**
```python
# Enable validation debugging
from arep.api.validation import validate_resource
try:
    validate_resource(resource)
except ValidationError as e:
    print(f"Validation failed: {e}")
```

#### 4. Memory Issues

**Symptoms:**
- Out of memory errors
- Slow performance with large datasets

**Solutions:**
```python
# Reduce memory usage
config = {
    'max_concurrent_entities': 2,  # Reduce concurrency
    'batch_size': 10,              # Smaller batches
    'enable_streaming': True       # Stream large datasets
}

# Monitor memory usage
import psutil
print(f"Memory usage: {psutil.virtual_memory().percent}%")
```

### Debugging Tools

#### Debug Mode

**Enable Debug Logging:**
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or for specific components
logging.getLogger('arep.enhancement').setLevel(logging.DEBUG)
logging.getLogger('arep.classification').setLevel(logging.DEBUG)
```

#### Pipeline Inspection

**Inspect Pipeline State:**
```python
# Check processing status
for entity_id, status in pipeline.processing_status.items():
    print(f"{entity_id}: {status.current_stage} - {status.status}")

# Check failed entities
print(f"Failed entities: {len(pipeline.failed_entities)}")
for entity_id in pipeline.failed_entities:
    status = pipeline.processing_status[entity_id]
    print(f"  {entity_id}: {status.error_message}")
```

#### Performance Profiling

**Profile Pipeline Performance:**
```python
import cProfile
import pstats

def profile_pipeline():
    profiler = cProfile.Profile()
    profiler.enable()

    # Run pipeline
    asyncio.run(pipeline.run())

    profiler.disable()
    stats = pstats.Stats(profiler)
    stats.sort_stats('cumulative')
    stats.print_stats(20)
```

### Error Recovery

#### Automatic Recovery

**Retry Configuration:**
```python
from tenacity import retry, stop_after_attempt, wait_exponential

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=4, max=10)
)
async def resilient_processing(entity):
    return await pipeline.process_single_entity(entity)
```

#### Manual Recovery

**Resume Failed Processing:**
```python
# Get failed entities
failed_entities = []
for entity_id in pipeline.failed_entities:
    status = pipeline.processing_status[entity_id]
    if status.error_message != "Unsupported entity type":
        failed_entities.append(entity_id)

# Retry failed entities
for entity_id in failed_entities:
    try:
        result = await pipeline.process_single_entity(entity)
        print(f"Recovered: {entity_id}")
    except Exception as e:
        print(f"Still failing: {entity_id} - {e}")
```

## Contributing

### Development Setup

#### Prerequisites
- Python 3.9+
- Git
- Virtual environment tools

#### Setup Steps

1. **Fork and Clone:**
```bash
git clone https://github.com/your-username/AI-nav_scrape_2.git
cd AI-nav_scrape_2
```

2. **Development Environment:**
```bash
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt  # Development dependencies
```

3. **Pre-commit Hooks:**
```bash
pre-commit install
```

#### Development Workflow

1. **Create Feature Branch:**
```bash
git checkout -b feature/new-enhancer
```

2. **Make Changes:**
- Follow existing code patterns
- Add comprehensive tests
- Update documentation

3. **Run Tests:**
```bash
python -m pytest tests/ -v
python -m pytest tests/ --cov=arep --cov-report=html
```

4. **Code Quality:**
```bash
black arep/  # Format code
isort arep/  # Sort imports
flake8 arep/  # Lint code
mypy arep/   # Type checking
```

5. **Submit Pull Request:**
- Ensure all tests pass
- Include clear description
- Reference related issues

### Code Standards

#### Style Guidelines
- **PEP 8** compliance
- **Black** code formatting
- **isort** import sorting
- **Type hints** for all functions
- **Docstrings** for all public methods

#### Testing Requirements
- **Unit tests** for all new functionality
- **Integration tests** for component interactions
- **Minimum 90% code coverage**
- **Mock external dependencies**

#### Documentation Standards
- **Clear docstrings** with examples
- **Type annotations** for all parameters
- **Usage examples** for new features
- **Update README** for significant changes

### Adding New Entity Types

#### 1. Define Entity Type

**Add to entity mapping:**
```python
# arep/classification/entity_mapping.py
ENTITY_TYPE_MAPPING = {
    "new_type": {
        "uuid": "new-uuid-here",
        "name": "New Type",
        "description": "Description of new type",
        "category": "category",
        "requires_fields": ["name", "website_url"],
        "optional_fields": ["specific_field"],
        "detail_type": "new_type_details"
    }
}
```

#### 2. Create Data Model

**Add to API models:**
```python
# arep/api/models.py
class NewTypeDetails(BaseModel):
    """Details specific to new type."""

    specific_field: Optional[str] = None
    another_field: List[str] = Field(default_factory=list)
```

#### 3. Implement Enhancer

**Create enhancer:**
```python
# arep/enhancement/new_type_enhancer.py
from arep.enhancement.base import BaseEnhancer

class NewTypeEnhancer(BaseEnhancer):
    def __init__(self):
        super().__init__("new_type")

    async def enhance(self, entity, research_data):
        base_fields = self._extract_base_fields(entity, research_data)

        # Type-specific enhancement logic
        new_type_details = self._create_new_type_details(research_data)

        return Resource(**base_fields, new_type_details=new_type_details)

    def _create_new_type_details(self, research_data):
        # Implementation specific to new type
        pass
```

#### 4. Register Enhancer

**Add to registry:**
```python
# arep/enhancement/registry.py
from arep.enhancement.new_type_enhancer import NewTypeEnhancer

def _register_default_enhancers(self):
    self._enhancer_classes.update({
        'tool': ToolEnhancer,
        'course': CourseEnhancer,
        'new_type': NewTypeEnhancer,  # Add new enhancer
    })
```

#### 5. Add Classification Patterns

**Update classifier:**
```python
# arep/classification/classifier.py
URL_PATTERNS = {
    "new_type": [
        r"newtype\.com",
        r".*\.newtype\..*",
        # Add URL patterns
    ]
}

NAME_KEYWORDS = {
    "new_type": [
        "new type keyword",
        "another keyword",
        # Add name patterns
    ]
}
```

#### 6. Create Tests

**Add comprehensive tests:**
```python
# tests/test_new_type_enhancer.py
class TestNewTypeEnhancer:
    @pytest.mark.asyncio
    async def test_new_type_enhancement_success(self):
        # Test implementation
        pass

    @pytest.mark.asyncio
    async def test_new_type_specific_features(self):
        # Test type-specific features
        pass
```

### Performance Optimization

#### Profiling Guidelines

**Profile Before Optimizing:**
```python
import cProfile
import pstats
from line_profiler import LineProfiler

# Function-level profiling
profiler = LineProfiler()
profiler.add_function(pipeline.process_single_entity)
profiler.enable_by_count()

# Run code
await pipeline.run()

profiler.print_stats()
```

#### Optimization Strategies

1. **Async/Await Optimization:**
   - Use `asyncio.gather()` for concurrent operations
   - Avoid blocking operations in async functions
   - Use connection pooling for HTTP requests

2. **Memory Optimization:**
   - Process entities in batches
   - Use generators for large datasets
   - Implement proper cleanup in context managers

3. **Caching Strategy:**
   - Cache research results for duplicate URLs
   - Cache classification results for similar entities
   - Use Redis for distributed caching

4. **Database Optimization:**
   - Use connection pooling
   - Implement proper indexing
   - Batch database operations

### Release Process

#### Version Management

**Semantic Versioning:**
- **Major** (X.0.0): Breaking changes
- **Minor** (0.X.0): New features, backward compatible
- **Patch** (0.0.X): Bug fixes, backward compatible

#### Release Checklist

1. **Pre-Release:**
   - [ ] All tests passing
   - [ ] Documentation updated
   - [ ] Version number updated
   - [ ] Changelog updated

2. **Release:**
   - [ ] Create release branch
   - [ ] Tag release version
   - [ ] Build and test release
   - [ ] Deploy to staging

3. **Post-Release:**
   - [ ] Deploy to production
   - [ ] Monitor for issues
   - [ ] Update documentation
   - [ ] Announce release

---

## Appendices

### A. Entity Type Reference

Complete reference of all 22 supported entity types with their characteristics:

| Type | UUID Pattern | Required Fields | Optional Fields |
|------|-------------|----------------|-----------------|
| tool | a1b2c3d4-... | name, website_url, short_description | key_features, has_api, has_free_tier |
| course | b2c3d4e5-... | name, website_url, short_description | instructor_name, duration_text, skill_level |
| agency | c3d4e5f6-... | name, website_url, short_description | services_offered, industry_focus |
| ... | ... | ... | ... |

### B. API Endpoint Reference

Complete reference of AI Navigator API endpoints used by the pipeline:

| Endpoint | Method | Purpose | Rate Limit |
|----------|--------|---------|------------|
| `/entities` | POST | Submit single entity | 10/sec |
| `/entities/batch` | POST | Submit multiple entities | 2/sec |
| `/health` | GET | Health check | 100/sec |

### C. Configuration Examples

#### Production Configuration
```json
{
  "pipeline": {
    "max_concurrent_entities": 10,
    "skip_failed_classification": false,
    "skip_unsupported_types": true,
    "timeout_seconds": 45
  },
  "research": {
    "enable_search_engines": true,
    "max_research_time": 120,
    "cache_results": true,
    "cache_ttl": 3600
  },
  "api": {
    "max_retries": 5,
    "retry_delay": 2.0,
    "batch_size": 20,
    "rate_limit": 8
  }
}
```

#### Development Configuration
```json
{
  "pipeline": {
    "max_concurrent_entities": 2,
    "skip_failed_classification": true,
    "skip_unsupported_types": true,
    "timeout_seconds": 15
  },
  "research": {
    "enable_search_engines": false,
    "max_research_time": 30,
    "cache_results": false
  },
  "api": {
    "max_retries": 2,
    "retry_delay": 1.0,
    "batch_size": 5,
    "rate_limit": 2
  }
}
```

### D. Glossary

**Entity**: An AI-related resource (tool, course, etc.) in the pipeline
**Classification**: Process of determining entity type
**Enhancement**: Process of enriching entity data
**Research**: Process of gathering additional entity information
**Pipeline**: Complete processing workflow
**Enhancer**: Component that transforms research data for specific entity types
**Scraper**: Component that collects entities from data sources
**Collector**: Orchestrator for multiple scrapers

---

*This documentation covers the complete AI Resource Enhancement Pipeline implementation. For additional support, please refer to the issue tracker or contact the development team.*
```
```
