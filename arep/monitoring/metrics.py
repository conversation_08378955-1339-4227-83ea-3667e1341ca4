"""
Monitoring and metrics collection for the AI Resource Enhancement Pipeline.
Provides comprehensive tracking of API interactions, performance metrics, and system health.
"""

import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from arep.utils.logger import get_logger

logger = get_logger(__name__)


class MetricType(Enum):
    """Types of metrics that can be collected."""

    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricPoint:
    """A single metric data point."""

    timestamp: float
    value: Union[int, float]
    tags: Dict[str, str] = field(default_factory=dict)


@dataclass
class APIMetrics:
    """Metrics for API interactions."""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rate_limited_requests: int = 0
    total_response_time: float = 0.0
    min_response_time: float = float("inf")
    max_response_time: float = 0.0

    # Response time buckets for histogram
    response_time_buckets: Dict[str, int] = field(
        default_factory=lambda: {
            "0-100ms": 0,
            "100-500ms": 0,
            "500ms-1s": 0,
            "1-5s": 0,
            "5s+": 0,
        }
    )

    # Status code tracking
    status_codes: Dict[int, int] = field(default_factory=lambda: defaultdict(int))

    # Error tracking
    error_types: Dict[str, int] = field(default_factory=lambda: defaultdict(int))

    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.successful_requests / self.total_requests) * 100

    @property
    def average_response_time(self) -> float:
        """Calculate average response time."""
        if self.successful_requests == 0:
            return 0.0
        return self.total_response_time / self.successful_requests

    def record_request(
        self,
        success: bool,
        response_time: float,
        status_code: Optional[int] = None,
        error_type: Optional[str] = None,
        rate_limited: bool = False,
    ):
        """Record a single API request."""
        self.total_requests += 1

        if success:
            self.successful_requests += 1
            self.total_response_time += response_time
            self.min_response_time = min(self.min_response_time, response_time)
            self.max_response_time = max(self.max_response_time, response_time)

            # Update response time buckets
            if response_time < 0.1:
                self.response_time_buckets["0-100ms"] += 1
            elif response_time < 0.5:
                self.response_time_buckets["100-500ms"] += 1
            elif response_time < 1.0:
                self.response_time_buckets["500ms-1s"] += 1
            elif response_time < 5.0:
                self.response_time_buckets["1-5s"] += 1
            else:
                self.response_time_buckets["5s+"] += 1
        else:
            self.failed_requests += 1

        if rate_limited:
            self.rate_limited_requests += 1

        if status_code:
            self.status_codes[status_code] += 1

        if error_type:
            self.error_types[error_type] += 1


class MetricsCollector:
    """
    Centralized metrics collection and aggregation.
    Collects various types of metrics and provides reporting capabilities.
    """

    def __init__(self, retention_period: int = 3600):
        """
        Initialize metrics collector.

        Args:
            retention_period: How long to keep metrics in seconds
        """
        self.retention_period = retention_period

        # Metric storage
        self.counters: Dict[str, int] = defaultdict(int)
        self.gauges: Dict[str, float] = {}
        self.histograms: Dict[str, List[float]] = defaultdict(list)
        self.timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))

        # Time-series data
        self.time_series: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))

        # API-specific metrics
        self.api_metrics = APIMetrics()

        # System metrics
        self.system_start_time = time.time()
        self.last_cleanup = time.time()

    def increment_counter(
        self, name: str, value: int = 1, tags: Optional[Dict[str, str]] = None
    ):
        """Increment a counter metric."""
        key = self._make_key(name, tags)
        self.counters[key] += value
        self._add_time_series_point(name, self.counters[key], tags)

    def set_gauge(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """Set a gauge metric value."""
        key = self._make_key(name, tags)
        self.gauges[key] = value
        self._add_time_series_point(name, value, tags)

    def record_histogram(
        self, name: str, value: float, tags: Optional[Dict[str, str]] = None
    ):
        """Record a value in a histogram."""
        key = self._make_key(name, tags)
        self.histograms[key].append(value)
        self._add_time_series_point(name, value, tags)

        # Keep only recent values
        if len(self.histograms[key]) > 1000:
            self.histograms[key] = self.histograms[key][-1000:]

    def start_timer(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Start a timer and return a timer ID."""
        timer_id = f"{name}_{time.time()}_{id(self)}"
        self.timers[timer_id] = time.time()
        return timer_id

    def stop_timer(self, timer_id: str, tags: Optional[Dict[str, str]] = None) -> float:
        """Stop a timer and record the duration."""
        if timer_id not in self.timers:
            logger.warning(f"Timer {timer_id} not found")
            return 0.0

        start_time = self.timers[timer_id]
        duration = time.time() - start_time
        del self.timers[timer_id]

        # Extract name from timer_id
        name = timer_id.split("_")[0]
        self.record_histogram(f"{name}_duration", duration, tags)

        return duration

    def record_api_request(
        self,
        endpoint: str,
        method: str,
        success: bool,
        response_time: float,
        status_code: Optional[int] = None,
        error_type: Optional[str] = None,
        rate_limited: bool = False,
    ):
        """Record an API request with comprehensive metrics."""
        # Update global API metrics
        self.api_metrics.record_request(
            success=success,
            response_time=response_time,
            status_code=status_code,
            error_type=error_type,
            rate_limited=rate_limited,
        )

        # Record detailed metrics with tags
        tags = {"endpoint": endpoint, "method": method}

        self.increment_counter("api_requests_total", tags=tags)

        if success:
            self.increment_counter("api_requests_successful", tags=tags)
            self.record_histogram("api_response_time", response_time, tags=tags)
        else:
            self.increment_counter("api_requests_failed", tags=tags)
            if error_type:
                self.increment_counter(
                    "api_errors", tags={**tags, "error_type": error_type}
                )

        if rate_limited:
            self.increment_counter("api_rate_limited", tags=tags)

        if status_code:
            self.increment_counter(
                "api_status_codes", tags={**tags, "status_code": str(status_code)}
            )

    def record_batch_operation(
        self,
        batch_size: int,
        successful_items: int,
        failed_items: int,
        duration: float,
        strategy: str,
    ):
        """Record batch processing metrics."""
        tags = {"strategy": strategy}

        self.record_histogram("batch_size", batch_size, tags=tags)
        self.record_histogram("batch_duration", duration, tags=tags)
        self.record_histogram(
            "batch_success_rate", (successful_items / batch_size) * 100, tags=tags
        )

        self.increment_counter("batch_operations_total", tags=tags)
        self.increment_counter(
            "batch_items_processed", successful_items + failed_items, tags=tags
        )
        self.increment_counter("batch_items_successful", successful_items, tags=tags)
        self.increment_counter("batch_items_failed", failed_items, tags=tags)

    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of all collected metrics."""
        now = time.time()
        uptime = now - self.system_start_time

        summary = {
            "system": {
                "uptime_seconds": uptime,
                "uptime_human": self._format_duration(uptime),
                "metrics_collected_at": datetime.now().isoformat(),
            },
            "api": {
                "total_requests": self.api_metrics.total_requests,
                "successful_requests": self.api_metrics.successful_requests,
                "failed_requests": self.api_metrics.failed_requests,
                "success_rate_percent": round(self.api_metrics.success_rate, 2),
                "average_response_time_ms": round(
                    self.api_metrics.average_response_time * 1000, 2
                ),
                "min_response_time_ms": round(
                    self.api_metrics.min_response_time * 1000, 2
                )
                if self.api_metrics.min_response_time != float("inf")
                else 0,
                "max_response_time_ms": round(
                    self.api_metrics.max_response_time * 1000, 2
                ),
                "rate_limited_requests": self.api_metrics.rate_limited_requests,
                "response_time_distribution": self.api_metrics.response_time_buckets,
                "status_codes": dict(self.api_metrics.status_codes),
                "error_types": dict(self.api_metrics.error_types),
            },
            "counters": dict(self.counters),
            "gauges": dict(self.gauges),
        }

        # Add histogram summaries
        histogram_summaries = {}
        for name, values in self.histograms.items():
            if values:
                histogram_summaries[name] = {
                    "count": len(values),
                    "min": min(values),
                    "max": max(values),
                    "avg": sum(values) / len(values),
                    "p50": self._percentile(values, 50),
                    "p95": self._percentile(values, 95),
                    "p99": self._percentile(values, 99),
                }
        summary["histograms"] = histogram_summaries

        return summary

    def get_health_status(self) -> Dict[str, Any]:
        """Get system health status based on metrics."""
        health = {
            "status": "healthy",
            "checks": {},
            "timestamp": datetime.now().isoformat(),
        }

        # Check API success rate
        if self.api_metrics.total_requests > 0:
            success_rate = self.api_metrics.success_rate
            if success_rate < 50:
                health["status"] = "unhealthy"
                health["checks"]["api_success_rate"] = {
                    "status": "fail",
                    "message": f"Low success rate: {success_rate:.1f}%",
                }
            elif success_rate < 80:
                health["status"] = "degraded"
                health["checks"]["api_success_rate"] = {
                    "status": "warn",
                    "message": f"Moderate success rate: {success_rate:.1f}%",
                }
            else:
                health["checks"]["api_success_rate"] = {
                    "status": "pass",
                    "message": f"Good success rate: {success_rate:.1f}%",
                }

        # Check response times
        if self.api_metrics.successful_requests > 0:
            avg_response_time = self.api_metrics.average_response_time
            if avg_response_time > 10:
                health["status"] = "degraded"
                health["checks"]["response_time"] = {
                    "status": "warn",
                    "message": f"High response time: {avg_response_time:.2f}s",
                }
            else:
                health["checks"]["response_time"] = {
                    "status": "pass",
                    "message": f"Good response time: {avg_response_time:.2f}s",
                }

        # Check rate limiting
        if self.api_metrics.rate_limited_requests > 0:
            rate_limit_ratio = (
                self.api_metrics.rate_limited_requests / self.api_metrics.total_requests
            )
            if rate_limit_ratio > 0.1:
                health["status"] = "degraded"
                health["checks"]["rate_limiting"] = {
                    "status": "warn",
                    "message": f"High rate limiting: {rate_limit_ratio:.1%}",
                }
            else:
                health["checks"]["rate_limiting"] = {
                    "status": "pass",
                    "message": f"Low rate limiting: {rate_limit_ratio:.1%}",
                }

        return health

    def cleanup_old_metrics(self):
        """Clean up old metrics to prevent memory leaks."""
        now = time.time()
        if now - self.last_cleanup < 300:  # Clean up every 5 minutes
            return

        cutoff_time = now - self.retention_period

        # Clean up time series data
        for name, points in self.time_series.items():
            while points and points[0].timestamp < cutoff_time:
                points.popleft()

        self.last_cleanup = now

    def _make_key(self, name: str, tags: Optional[Dict[str, str]] = None) -> str:
        """Create a unique key for a metric with tags."""
        if not tags:
            return name

        tag_str = ",".join(f"{k}={v}" for k, v in sorted(tags.items()))
        return f"{name}[{tag_str}]"

    def _add_time_series_point(
        self, name: str, value: float, tags: Optional[Dict[str, str]] = None
    ):
        """Add a point to time series data."""
        key = self._make_key(name, tags)
        point = MetricPoint(timestamp=time.time(), value=value, tags=tags or {})
        self.time_series[key].append(point)

    def _percentile(self, values: List[float], percentile: int) -> float:
        """Calculate percentile of a list of values."""
        if not values:
            return 0.0

        sorted_values = sorted(values)
        index = int((percentile / 100) * len(sorted_values))
        index = min(index, len(sorted_values) - 1)
        return sorted_values[index]

    def _format_duration(self, seconds: float) -> str:
        """Format duration in human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds/60:.1f}m"
        elif seconds < 86400:
            return f"{seconds/3600:.1f}h"
        else:
            return f"{seconds/86400:.1f}d"


# Global metrics collector instance
_global_metrics = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _global_metrics
    if _global_metrics is None:
        _global_metrics = MetricsCollector()
    return _global_metrics


def reset_metrics():
    """Reset the global metrics collector (useful for testing)."""
    global _global_metrics
    _global_metrics = None
