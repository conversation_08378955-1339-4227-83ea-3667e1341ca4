"""
Favicon extraction utilities for the AI Resource Enhancement Pipeline.
Provides functions to extract and generate favicon URLs for entities.
"""

import re
from typing import Optional
from urllib.parse import urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup

from arep.utils.logger import get_logger

logger = get_logger(__name__)


def get_favicon_url(url: str, size: int = 128) -> str:
    """
    Generate a favicon URL using Google's favicon service as fallback.
    
    Args:
        url: Website URL
        size: Favicon size in pixels (default: 128)
        
    Returns:
        Favicon URL
    """
    parsed_url = urlparse(url)
    domain = parsed_url.netloc or parsed_url.path
    
    # Remove 'www.' prefix if present for cleaner domain
    if domain.startswith('www.'):
        domain = domain[4:]
    
    return f"https://www.google.com/s2/favicons?domain={domain}&sz={size}"


async def extract_favicon_from_page(url: str, session: Optional[aiohttp.ClientSession] = None) -> Optional[str]:
    """
    Extract favicon URL directly from a webpage's HTML.
    
    Args:
        url: Website URL to extract favicon from
        session: Optional aiohttp session to use
        
    Returns:
        Favicon URL if found, None otherwise
    """
    close_session = False
    if session is None:
        session = aiohttp.ClientSession()
        close_session = True
    
    try:
        async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
            if response.status != 200:
                return None
            
            html = await response.text()
            soup = BeautifulSoup(html, 'html.parser')
            
            # Look for various favicon link tags
            favicon_selectors = [
                'link[rel="icon"]',
                'link[rel="shortcut icon"]',
                'link[rel="apple-touch-icon"]',
                'link[rel="apple-touch-icon-precomposed"]',
            ]
            
            for selector in favicon_selectors:
                favicon_tag = soup.select_one(selector)
                if favicon_tag and favicon_tag.get('href'):
                    favicon_url = favicon_tag['href']
                    
                    # Convert relative URLs to absolute
                    if favicon_url.startswith('//'):
                        favicon_url = f"{urlparse(url).scheme}:{favicon_url}"
                    elif favicon_url.startswith('/'):
                        favicon_url = urljoin(url, favicon_url)
                    elif not favicon_url.startswith(('http://', 'https://')):
                        favicon_url = urljoin(url, favicon_url)
                    
                    return favicon_url
            
            # Fallback: try common favicon paths
            common_paths = ['/favicon.ico', '/favicon.png', '/apple-touch-icon.png']
            base_url = f"{urlparse(url).scheme}://{urlparse(url).netloc}"
            
            for path in common_paths:
                favicon_url = urljoin(base_url, path)
                try:
                    async with session.head(favicon_url, timeout=aiohttp.ClientTimeout(total=5)) as favicon_response:
                        if favicon_response.status == 200:
                            return favicon_url
                except:
                    continue
            
            return None
            
    except Exception as e:
        logger.debug(f"Error extracting favicon from {url}: {e}")
        return None
    
    finally:
        if close_session:
            await session.close()


async def get_best_favicon_url(url: str, session: Optional[aiohttp.ClientSession] = None) -> str:
    """
    Get the best available favicon URL for a website.
    First tries to extract from the page, then falls back to Google's service.
    
    Args:
        url: Website URL
        session: Optional aiohttp session to use
        
    Returns:
        Best available favicon URL
    """
    # First try to extract from the page
    extracted_favicon = await extract_favicon_from_page(url, session)
    if extracted_favicon:
        return extracted_favicon
    
    # Fallback to Google's favicon service
    return get_favicon_url(url)


def is_valid_favicon_url(url: str) -> bool:
    """
    Check if a URL appears to be a valid favicon URL.
    
    Args:
        url: URL to validate
        
    Returns:
        True if URL appears to be a favicon, False otherwise
    """
    if not url:
        return False
    
    # Check for common favicon file extensions
    favicon_extensions = ['.ico', '.png', '.jpg', '.jpeg', '.gif', '.svg']
    url_lower = url.lower()
    
    # Check if URL ends with a favicon extension
    if any(url_lower.endswith(ext) for ext in favicon_extensions):
        return True
    
    # Check for common favicon keywords in URL
    favicon_keywords = ['favicon', 'icon', 'apple-touch-icon']
    if any(keyword in url_lower for keyword in favicon_keywords):
        return True
    
    # Check for Google's favicon service
    if 'google.com/s2/favicons' in url_lower:
        return True
    
    return False


def normalize_favicon_url(url: str, base_url: str) -> str:
    """
    Normalize a favicon URL to ensure it's absolute and properly formatted.
    
    Args:
        url: Favicon URL to normalize
        base_url: Base URL of the website
        
    Returns:
        Normalized favicon URL
    """
    if not url:
        return get_favicon_url(base_url)
    
    # If already absolute, return as-is
    if url.startswith(('http://', 'https://')):
        return url
    
    # Handle protocol-relative URLs
    if url.startswith('//'):
        return f"{urlparse(base_url).scheme}:{url}"
    
    # Handle absolute paths
    if url.startswith('/'):
        parsed_base = urlparse(base_url)
        return f"{parsed_base.scheme}://{parsed_base.netloc}{url}"
    
    # Handle relative paths
    return urljoin(base_url, url)


class FaviconExtractor:
    """
    Advanced favicon extraction with caching and multiple strategies.
    """
    
    def __init__(self, cache_size: int = 1000):
        """
        Initialize the favicon extractor.
        
        Args:
            cache_size: Maximum number of favicon URLs to cache
        """
        self.cache = {}
        self.cache_size = cache_size
    
    async def get_favicon(self, url: str, session: Optional[aiohttp.ClientSession] = None) -> str:
        """
        Get favicon URL with caching.
        
        Args:
            url: Website URL
            session: Optional aiohttp session
            
        Returns:
            Favicon URL
        """
        # Check cache first
        domain = urlparse(url).netloc
        if domain in self.cache:
            return self.cache[domain]
        
        # Extract favicon
        favicon_url = await get_best_favicon_url(url, session)
        
        # Cache the result
        if len(self.cache) >= self.cache_size:
            # Remove oldest entry (simple FIFO)
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[domain] = favicon_url
        return favicon_url
    
    def clear_cache(self):
        """Clear the favicon cache."""
        self.cache.clear()
    
    def get_cache_stats(self) -> dict:
        """Get cache statistics."""
        return {
            'cache_size': len(self.cache),
            'max_cache_size': self.cache_size,
            'cached_domains': list(self.cache.keys()),
        }
