"""
Content scraping utilities for extracting and analyzing web content.
Provides tools for extracting structured data from web pages.
"""

import re
from typing import Dict, List, Optional, Set
from urllib.parse import urljoin, urlparse

import aiohttp
from bs4 import BeautifulSoup, Tag

from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ContentExtractor:
    """
    Utility class for extracting structured content from web pages.
    """
    
    def __init__(self, timeout: int = 15):
        """
        Initialize the content extractor.
        
        Args:
            timeout: Request timeout in seconds
        """
        self.timeout = timeout
    
    async def extract_page_data(self, url: str) -> Dict[str, any]:
        """
        Extract comprehensive data from a web page.
        
        Args:
            url: URL to extract data from
            
        Returns:
            Dictionary containing extracted data
        """
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.get(url) as response:
                    if response.status != 200:
                        return {"error": f"HTTP {response.status}"}
                    
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')
                    
                    return {
                        "title": self._extract_title(soup),
                        "description": self._extract_description(soup),
                        "keywords": self._extract_keywords(soup),
                        "headings": self._extract_headings(soup),
                        "links": self._extract_links(soup, url),
                        "images": self._extract_images(soup, url),
                        "meta_data": self._extract_meta_data(soup),
                        "structured_data": self._extract_structured_data(soup),
                        "content_text": self._extract_clean_text(soup),
                        "language": self._detect_language(soup),
                        "social_links": self._extract_social_links(soup),
                        "contact_info": self._extract_contact_info(soup),
                        "pricing_info": self._extract_pricing_info(soup),
                        "features": self._extract_features(soup),
                    }
        
        except Exception as e:
            logger.error(f"Error extracting data from {url}: {e}")
            return {"error": str(e)}
    
    def _extract_title(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract page title."""
        title_tag = soup.find('title')
        if title_tag:
            return title_tag.get_text().strip()
        
        # Fallback to h1
        h1_tag = soup.find('h1')
        if h1_tag:
            return h1_tag.get_text().strip()
        
        return None
    
    def _extract_description(self, soup: BeautifulSoup) -> Optional[str]:
        """Extract page description from meta tags."""
        # Try meta description first
        desc_tag = soup.find('meta', attrs={'name': 'description'})
        if desc_tag and desc_tag.get('content'):
            return desc_tag['content'].strip()
        
        # Try Open Graph description
        og_desc = soup.find('meta', attrs={'property': 'og:description'})
        if og_desc and og_desc.get('content'):
            return og_desc['content'].strip()
        
        # Try Twitter description
        twitter_desc = soup.find('meta', attrs={'name': 'twitter:description'})
        if twitter_desc and twitter_desc.get('content'):
            return twitter_desc['content'].strip()
        
        return None
    
    def _extract_keywords(self, soup: BeautifulSoup) -> List[str]:
        """Extract keywords from meta tags."""
        keywords_tag = soup.find('meta', attrs={'name': 'keywords'})
        if keywords_tag and keywords_tag.get('content'):
            keywords = keywords_tag['content'].split(',')
            return [kw.strip() for kw in keywords if kw.strip()]
        return []
    
    def _extract_headings(self, soup: BeautifulSoup) -> Dict[str, List[str]]:
        """Extract all headings organized by level."""
        headings = {}
        for level in range(1, 7):  # h1 to h6
            tag_name = f'h{level}'
            tags = soup.find_all(tag_name)
            headings[tag_name] = [tag.get_text().strip() for tag in tags]
        return headings
    
    def _extract_links(self, soup: BeautifulSoup, base_url: str) -> Dict[str, List[str]]:
        """Extract and categorize links."""
        links = {"internal": [], "external": []}
        base_domain = urlparse(base_url).netloc
        
        for link in soup.find_all('a', href=True):
            href = link['href']
            
            # Convert relative URLs to absolute
            if href.startswith('/'):
                href = urljoin(base_url, href)
            elif not href.startswith(('http://', 'https://')):
                href = urljoin(base_url, href)
            
            # Categorize as internal or external
            link_domain = urlparse(href).netloc
            if link_domain == base_domain:
                links["internal"].append(href)
            else:
                links["external"].append(href)
        
        return links
    
    def _extract_images(self, soup: BeautifulSoup, base_url: str) -> List[Dict[str, str]]:
        """Extract image information."""
        images = []
        
        for img in soup.find_all('img'):
            src = img.get('src')
            if src:
                # Convert relative URLs to absolute
                if src.startswith('/'):
                    src = urljoin(base_url, src)
                elif not src.startswith(('http://', 'https://')):
                    src = urljoin(base_url, src)
                
                images.append({
                    "src": src,
                    "alt": img.get('alt', ''),
                    "title": img.get('title', ''),
                })
        
        return images
    
    def _extract_meta_data(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract all meta tag data."""
        meta_data = {}
        
        for meta in soup.find_all('meta'):
            # Handle name-content pairs
            if meta.get('name') and meta.get('content'):
                meta_data[f"meta_{meta['name']}"] = meta['content']
            
            # Handle property-content pairs (Open Graph, etc.)
            if meta.get('property') and meta.get('content'):
                meta_data[f"property_{meta['property']}"] = meta['content']
        
        return meta_data
    
    def _extract_structured_data(self, soup: BeautifulSoup) -> List[Dict]:
        """Extract JSON-LD structured data."""
        structured_data = []
        
        for script in soup.find_all('script', type='application/ld+json'):
            try:
                import json
                data = json.loads(script.string)
                structured_data.append(data)
            except (json.JSONDecodeError, AttributeError):
                continue
        
        return structured_data
    
    def _extract_clean_text(self, soup: BeautifulSoup) -> str:
        """Extract clean text content from the page."""
        # Remove script and style elements
        for script in soup(["script", "style"]):
            script.decompose()
        
        # Get text and clean it up
        text = soup.get_text()
        
        # Clean up whitespace
        lines = (line.strip() for line in text.splitlines())
        chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
        text = ' '.join(chunk for chunk in chunks if chunk)
        
        return text
    
    def _detect_language(self, soup: BeautifulSoup) -> Optional[str]:
        """Detect page language."""
        # Check html lang attribute
        html_tag = soup.find('html')
        if html_tag and html_tag.get('lang'):
            return html_tag['lang']
        
        # Check meta language
        lang_meta = soup.find('meta', attrs={'name': 'language'})
        if lang_meta and lang_meta.get('content'):
            return lang_meta['content']
        
        return None
    
    def _extract_social_links(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract social media links."""
        social_patterns = {
            'twitter': r'twitter\.com/[^/\s]+',
            'linkedin': r'linkedin\.com/(?:in|company)/[^/\s]+',
            'github': r'github\.com/[^/\s]+',
            'facebook': r'facebook\.com/[^/\s]+',
            'instagram': r'instagram\.com/[^/\s]+',
            'youtube': r'youtube\.com/(?:c/|channel/|user/)[^/\s]+',
        }
        
        social_links = {}
        page_text = str(soup)
        
        for platform, pattern in social_patterns.items():
            matches = re.findall(pattern, page_text, re.IGNORECASE)
            if matches:
                # Take the first match and ensure it's a full URL
                link = matches[0]
                if not link.startswith('http'):
                    link = f"https://{link}"
                social_links[platform] = link
        
        return social_links
    
    def _extract_contact_info(self, soup: BeautifulSoup) -> Dict[str, List[str]]:
        """Extract contact information."""
        contact_info = {"emails": [], "phones": []}
        
        text = soup.get_text()
        
        # Extract emails
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, text)
        contact_info["emails"] = list(set(emails))  # Remove duplicates
        
        # Extract phone numbers (basic pattern)
        phone_pattern = r'[\+]?[1-9]?[0-9]{7,15}'
        phones = re.findall(phone_pattern, text)
        contact_info["phones"] = list(set(phones))
        
        return contact_info
    
    def _extract_pricing_info(self, soup: BeautifulSoup) -> List[str]:
        """Extract pricing information."""
        pricing_keywords = ['price', 'pricing', 'cost', 'fee', 'subscription', 'plan']
        pricing_info = []
        
        # Look for elements containing pricing keywords
        for keyword in pricing_keywords:
            elements = soup.find_all(text=re.compile(keyword, re.IGNORECASE))
            for element in elements:
                if hasattr(element, 'parent'):
                    parent_text = element.parent.get_text().strip()
                    if len(parent_text) < 200:  # Avoid very long text
                        pricing_info.append(parent_text)
        
        return list(set(pricing_info))  # Remove duplicates
    
    def _extract_features(self, soup: BeautifulSoup) -> List[str]:
        """Extract feature lists and descriptions."""
        features = []
        
        # Look for common feature list patterns
        feature_selectors = [
            'ul li',  # Unordered lists
            '.feature',  # Elements with feature class
            '.features li',  # Features in lists
            '[class*="feature"]',  # Any class containing "feature"
        ]
        
        for selector in feature_selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text().strip()
                if text and len(text) < 100:  # Reasonable feature length
                    features.append(text)
        
        return list(set(features))  # Remove duplicates


class ContentAnalyzer:
    """
    Analyzes extracted content for classification and enhancement purposes.
    """
    
    def __init__(self):
        """Initialize the content analyzer."""
        self.extractor = ContentExtractor()
    
    async def analyze_for_classification(self, url: str) -> Dict[str, any]:
        """
        Analyze content specifically for entity classification.
        
        Args:
            url: URL to analyze
            
        Returns:
            Analysis results for classification
        """
        content_data = await self.extractor.extract_page_data(url)
        
        if "error" in content_data:
            return content_data
        
        analysis = {
            "classification_signals": self._extract_classification_signals(content_data),
            "content_type_indicators": self._identify_content_type_indicators(content_data),
            "business_model_signals": self._extract_business_model_signals(content_data),
            "technical_indicators": self._extract_technical_indicators(content_data),
            "audience_indicators": self._extract_audience_indicators(content_data),
        }
        
        return analysis
    
    def _extract_classification_signals(self, content_data: Dict) -> Dict[str, List[str]]:
        """Extract signals that help with entity type classification."""
        signals = {
            "tool_signals": [],
            "course_signals": [],
            "agency_signals": [],
            "research_signals": [],
            "news_signals": [],
            "software_signals": [],
        }
        
        text = content_data.get("content_text", "").lower()
        title = content_data.get("title", "").lower()
        description = content_data.get("description", "").lower()
        
        # Tool signals
        tool_keywords = ["api", "platform", "tool", "ai", "ml", "dashboard", "analytics"]
        for keyword in tool_keywords:
            if keyword in text or keyword in title:
                signals["tool_signals"].append(keyword)
        
        # Course signals
        course_keywords = ["course", "learn", "tutorial", "lesson", "module", "certificate"]
        for keyword in course_keywords:
            if keyword in text or keyword in title:
                signals["course_signals"].append(keyword)
        
        # Agency signals
        agency_keywords = ["agency", "consulting", "services", "portfolio", "clients"]
        for keyword in agency_keywords:
            if keyword in text or keyword in title:
                signals["agency_signals"].append(keyword)
        
        # Research signals
        research_keywords = ["research", "paper", "study", "analysis", "academic", "doi"]
        for keyword in research_keywords:
            if keyword in text or keyword in title:
                signals["research_signals"].append(keyword)
        
        # News signals
        news_keywords = ["news", "article", "breaking", "published", "reporter"]
        for keyword in news_keywords:
            if keyword in text or keyword in title:
                signals["news_signals"].append(keyword)
        
        # Software signals
        software_keywords = ["github", "open source", "library", "framework", "download"]
        for keyword in software_keywords:
            if keyword in text or keyword in title:
                signals["software_signals"].append(keyword)
        
        return signals
    
    def _identify_content_type_indicators(self, content_data: Dict) -> List[str]:
        """Identify content type indicators."""
        indicators = []
        
        # Check for specific HTML structures
        headings = content_data.get("headings", {})
        if headings.get("h1") and len(headings["h1"]) == 1:
            indicators.append("single_main_heading")
        
        if headings.get("h2") and len(headings["h2"]) > 3:
            indicators.append("multiple_sections")
        
        # Check for pricing information
        if content_data.get("pricing_info"):
            indicators.append("has_pricing")
        
        # Check for features
        if content_data.get("features"):
            indicators.append("has_features")
        
        # Check for contact information
        contact = content_data.get("contact_info", {})
        if contact.get("emails"):
            indicators.append("has_contact_email")
        
        return indicators
    
    def _extract_business_model_signals(self, content_data: Dict) -> List[str]:
        """Extract business model signals."""
        signals = []
        text = content_data.get("content_text", "").lower()
        
        business_keywords = {
            "freemium": ["free", "premium", "upgrade", "pro"],
            "subscription": ["subscription", "monthly", "yearly", "recurring"],
            "one_time": ["one time", "lifetime", "purchase", "buy now"],
            "open_source": ["open source", "free", "github", "mit license"],
            "enterprise": ["enterprise", "business", "team", "organization"],
        }
        
        for model, keywords in business_keywords.items():
            if any(keyword in text for keyword in keywords):
                signals.append(model)
        
        return signals
    
    def _extract_technical_indicators(self, content_data: Dict) -> List[str]:
        """Extract technical indicators."""
        indicators = []
        text = content_data.get("content_text", "").lower()
        
        tech_keywords = [
            "api", "sdk", "python", "javascript", "react", "node",
            "docker", "kubernetes", "aws", "cloud", "database",
            "machine learning", "artificial intelligence", "neural network"
        ]
        
        for keyword in tech_keywords:
            if keyword in text:
                indicators.append(keyword)
        
        return indicators
    
    def _extract_audience_indicators(self, content_data: Dict) -> List[str]:
        """Extract target audience indicators."""
        indicators = []
        text = content_data.get("content_text", "").lower()
        
        audience_keywords = {
            "developers": ["developer", "programmer", "coder", "engineer"],
            "businesses": ["business", "enterprise", "company", "organization"],
            "students": ["student", "learner", "beginner", "education"],
            "researchers": ["researcher", "academic", "scientist", "scholar"],
            "designers": ["designer", "creative", "artist", "visual"],
        }
        
        for audience, keywords in audience_keywords.items():
            if any(keyword in text for keyword in keywords):
                indicators.append(audience)
        
        return indicators
