"""
LLM-based entity classification for improved accuracy.
Uses large language models to classify entities based on content analysis.
"""

import json
from typing import Dict, List, Optional

import aiohttp

from arep.config import OPENAI_API_KEY
from arep.models import ClassificationResult, MinimalEntity
from arep.utils.content_scraper import ContentAnalyzer
from arep.utils.logger import get_logger
from .classifier import BaseClassifier

logger = get_logger(__name__)


class LLMClassifier(BaseClassifier):
    """
    LLM-based classifier using OpenAI's API for entity classification.
    Provides high-accuracy classification based on content analysis.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-3.5-turbo"):
        """
        Initialize the LLM classifier.
        
        Args:
            api_key: OpenAI API key (defaults to config)
            model: OpenAI model to use
        """
        super().__init__("llm_classifier")
        self.api_key = api_key or OPENAI_API_KEY
        self.model = model
        self.content_analyzer = ContentAnalyzer()
        
        # Entity type definitions for the LLM
        self.entity_types = {
            "tool": "AI/ML tools, platforms, applications, APIs, or software services",
            "course": "Educational courses, tutorials, training programs, or learning materials",
            "agency": "Consulting agencies, service providers, or professional services companies",
            "content_creator": "Individual bloggers, writers, influencers, or content creators",
            "community": "Online communities, forums, Discord servers, or social groups",
            "newsletter": "Email newsletters, weekly digests, or subscription-based content",
            "dataset": "Data collections, datasets, or data repositories",
            "research_paper": "Academic papers, research studies, or scientific publications",
            "software": "Open source software, libraries, frameworks, or development tools",
            "model": "AI/ML models, checkpoints, or pre-trained models",
            "project_reference": "Example projects, demos, or reference implementations",
            "service_provider": "Cloud services, hosting providers, or infrastructure services",
            "investor": "Venture capital firms, investors, or funding organizations",
            "event": "Conferences, meetups, workshops, or industry events",
            "job": "Job postings, career opportunities, or recruitment listings",
            "grant": "Grants, funding opportunities, or financial awards",
            "bounty": "Bug bounties, challenges, or reward programs",
            "hardware": "Computer hardware, GPUs, processors, or physical devices",
            "news": "News articles, press releases, or journalism content",
            "book": "Books, ebooks, guides, or published literature",
            "podcast": "Podcasts, audio content, or interview series",
            "platform": "Platforms, marketplaces, or multi-service ecosystems"
        }
    
    def _create_classification_prompt(self, entity: MinimalEntity, content_data: Dict) -> str:
        """
        Create a prompt for LLM classification.
        
        Args:
            entity: Entity to classify
            content_data: Extracted content data
            
        Returns:
            Classification prompt
        """
        # Extract key information
        title = content_data.get("title", "")
        description = content_data.get("description", "")
        content_text = content_data.get("content_text", "")[:2000]  # Limit content length
        features = content_data.get("features", [])[:10]  # Limit features
        pricing_info = content_data.get("pricing_info", [])[:5]  # Limit pricing info
        
        # Create entity type descriptions
        type_descriptions = "\n".join([
            f"- {type_name}: {description}"
            for type_name, description in self.entity_types.items()
        ])
        
        prompt = f"""
You are an expert at classifying AI/tech entities. Analyze the following entity and classify it into one of the predefined types.

Entity Information:
- Name: {entity.name}
- URL: {entity.url}
- Title: {title}
- Description: {description}

Content Analysis:
- Features: {features}
- Pricing Info: {pricing_info}
- Content Preview: {content_text}

Available Entity Types:
{type_descriptions}

Instructions:
1. Analyze the entity's name, URL, content, and features
2. Determine the most appropriate entity type from the list above
3. Provide a confidence score between 0.0 and 1.0
4. Explain your reasoning in 1-2 sentences
5. Suggest up to 2 alternative types if applicable

Respond in JSON format:
{{
    "entity_type": "selected_type",
    "confidence": 0.85,
    "reasoning": "Brief explanation of classification decision",
    "alternative_types": ["alternative1", "alternative2"]
}}
"""
        return prompt
    
    async def _call_openai_api(self, prompt: str) -> Optional[Dict]:
        """
        Call OpenAI API for classification.
        
        Args:
            prompt: Classification prompt
            
        Returns:
            API response or None if failed
        """
        if not self.api_key:
            logger.warning("OpenAI API key not configured")
            return None
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }
        
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert AI entity classifier. Always respond with valid JSON."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,  # Low temperature for consistent results
            "max_tokens": 500,
        }
        
        try:
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result["choices"][0]["message"]["content"]
                        
                        # Parse JSON response
                        try:
                            return json.loads(content)
                        except json.JSONDecodeError:
                            # Try to extract JSON from response
                            import re
                            json_match = re.search(r'\{.*\}', content, re.DOTALL)
                            if json_match:
                                return json.loads(json_match.group())
                            return None
                    else:
                        logger.error(f"OpenAI API error: {response.status}")
                        return None
        
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {e}")
            return None
    
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify entity using LLM analysis.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Classification result
        """
        logger.debug(f"LLM classifying entity: {entity.name}")
        
        # Extract content data for analysis
        try:
            content_data = await self.content_analyzer.extractor.extract_page_data(str(entity.url))
            
            if "error" in content_data:
                logger.warning(f"Could not extract content for {entity.url}: {content_data['error']}")
                # Fallback to basic classification
                return ClassificationResult(
                    entity_type="tool",
                    entity_type_id="00000000-0000-0000-0000-000000000000",
                    confidence=0.3,
                    reasoning="Could not extract content for LLM analysis, using fallback",
                )
        
        except Exception as e:
            logger.error(f"Error extracting content for {entity.url}: {e}")
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.2,
                reasoning=f"Content extraction failed: {e}",
            )
        
        # Create classification prompt
        prompt = self._create_classification_prompt(entity, content_data)
        
        # Call LLM API
        llm_response = await self._call_openai_api(prompt)
        
        if not llm_response:
            logger.warning(f"LLM API call failed for {entity.name}")
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.2,
                reasoning="LLM API call failed, using fallback",
            )
        
        # Parse LLM response
        try:
            entity_type = llm_response.get("entity_type", "tool")
            confidence = float(llm_response.get("confidence", 0.5))
            reasoning = llm_response.get("reasoning", "LLM classification")
            alternative_types = llm_response.get("alternative_types", [])
            
            # Validate entity type
            if entity_type not in self.entity_types:
                logger.warning(f"Invalid entity type from LLM: {entity_type}")
                entity_type = "tool"
                confidence = 0.3
                reasoning = f"Invalid type '{entity_type}' from LLM, defaulting to tool"
            
            # Ensure confidence is in valid range
            confidence = max(0.0, min(1.0, confidence))
            
            logger.debug(f"LLM classified {entity.name} as {entity_type} (confidence: {confidence:.2f})")
            
            return ClassificationResult(
                entity_type=entity_type,
                entity_type_id="00000000-0000-0000-0000-000000000000",  # Will be mapped later
                confidence=confidence,
                reasoning=f"LLM analysis: {reasoning}",
                alternative_types=alternative_types,
            )
        
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return ClassificationResult(
                entity_type="tool",
                entity_type_id="00000000-0000-0000-0000-000000000000",
                confidence=0.2,
                reasoning=f"Error parsing LLM response: {e}",
            )


class HybridLLMClassifier(BaseClassifier):
    """
    Hybrid classifier that combines traditional methods with LLM analysis.
    Uses LLM for uncertain cases and traditional methods for clear cases.
    """
    
    def __init__(self, api_key: Optional[str] = None, llm_threshold: float = 0.7):
        """
        Initialize the hybrid classifier.
        
        Args:
            api_key: OpenAI API key
            llm_threshold: Confidence threshold for using LLM (0.0-1.0)
        """
        super().__init__("hybrid_llm")
        self.llm_classifier = LLMClassifier(api_key)
        self.llm_threshold = llm_threshold
        
        # Import traditional classifiers
        from .classifier import URLPatternClassifier, NameBasedClassifier
        self.url_classifier = URLPatternClassifier()
        self.name_classifier = NameBasedClassifier()
    
    async def classify(self, entity: MinimalEntity) -> ClassificationResult:
        """
        Classify entity using hybrid approach.
        
        Args:
            entity: Entity to classify
            
        Returns:
            Classification result
        """
        # First try traditional classifiers
        url_result = await self.url_classifier.classify(entity)
        name_result = await self.name_classifier.classify(entity)
        
        # Combine traditional results
        if url_result.confidence > name_result.confidence:
            traditional_result = url_result
        else:
            traditional_result = name_result
        
        # If traditional classification is confident enough, use it
        if traditional_result.confidence >= self.llm_threshold:
            logger.debug(f"Using traditional classification for {entity.name} (confidence: {traditional_result.confidence:.2f})")
            return traditional_result
        
        # Otherwise, use LLM for better accuracy
        logger.debug(f"Using LLM classification for {entity.name} (traditional confidence: {traditional_result.confidence:.2f})")
        llm_result = await self.llm_classifier.classify(entity)
        
        # If LLM fails, fall back to traditional
        if llm_result.confidence < 0.3:
            return traditional_result
        
        return llm_result
