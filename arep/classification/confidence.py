"""
Confidence scoring system for entity classification.
Provides sophisticated confidence calculation and calibration for classification results.
"""

import math
from typing import Dict, List, Optional, Tuple

from arep.models import ClassificationResult, MinimalEntity
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ConfidenceScorer:
    """
    Calculates and calibrates confidence scores for entity classification.
    Uses multiple signals to provide accurate confidence estimates.
    """
    
    def __init__(self):
        """Initialize the confidence scorer."""
        # Weights for different confidence factors
        self.factor_weights = {
            "classifier_agreement": 0.3,
            "signal_strength": 0.25,
            "content_quality": 0.2,
            "url_reliability": 0.15,
            "name_clarity": 0.1,
        }
        
        # Confidence thresholds
        self.thresholds = {
            "high_confidence": 0.8,
            "medium_confidence": 0.6,
            "low_confidence": 0.4,
        }
    
    def calculate_confidence(
        self,
        entity: MinimalEntity,
        classification_results: List[ClassificationResult],
        content_data: Optional[Dict] = None,
    ) -> float:
        """
        Calculate overall confidence score for classification.
        
        Args:
            entity: Entity being classified
            classification_results: Results from multiple classifiers
            content_data: Optional content analysis data
            
        Returns:
            Confidence score between 0.0 and 1.0
        """
        if not classification_results:
            return 0.0
        
        # Calculate individual confidence factors
        factors = {
            "classifier_agreement": self._calculate_classifier_agreement(classification_results),
            "signal_strength": self._calculate_signal_strength(classification_results),
            "content_quality": self._calculate_content_quality(content_data),
            "url_reliability": self._calculate_url_reliability(entity),
            "name_clarity": self._calculate_name_clarity(entity),
        }
        
        # Calculate weighted confidence
        weighted_confidence = sum(
            factors[factor] * weight
            for factor, weight in self.factor_weights.items()
        )
        
        # Apply calibration
        calibrated_confidence = self._calibrate_confidence(weighted_confidence, factors)
        
        # Ensure confidence is in valid range
        return max(0.0, min(1.0, calibrated_confidence))
    
    def _calculate_classifier_agreement(self, results: List[ClassificationResult]) -> float:
        """
        Calculate agreement between multiple classifiers.
        
        Args:
            results: Classification results from multiple classifiers
            
        Returns:
            Agreement score between 0.0 and 1.0
        """
        if len(results) <= 1:
            return results[0].confidence if results else 0.0
        
        # Count votes for each entity type
        type_votes = {}
        total_confidence = 0
        
        for result in results:
            entity_type = result.entity_type
            confidence = result.confidence
            
            if entity_type not in type_votes:
                type_votes[entity_type] = {"count": 0, "total_confidence": 0}
            
            type_votes[entity_type]["count"] += 1
            type_votes[entity_type]["total_confidence"] += confidence
            total_confidence += confidence
        
        # Find the most agreed-upon type
        best_type = max(type_votes.keys(), key=lambda t: type_votes[t]["count"])
        best_votes = type_votes[best_type]["count"]
        best_confidence = type_votes[best_type]["total_confidence"] / best_votes
        
        # Calculate agreement score
        agreement_ratio = best_votes / len(results)
        confidence_factor = best_confidence
        
        # Combine agreement ratio and confidence
        agreement_score = (agreement_ratio * 0.7) + (confidence_factor * 0.3)
        
        return agreement_score
    
    def _calculate_signal_strength(self, results: List[ClassificationResult]) -> float:
        """
        Calculate strength of classification signals.
        
        Args:
            results: Classification results
            
        Returns:
            Signal strength score between 0.0 and 1.0
        """
        if not results:
            return 0.0
        
        # Get the best result
        best_result = max(results, key=lambda r: r.confidence)
        
        # Base signal strength from confidence
        signal_strength = best_result.confidence
        
        # Boost if multiple classifiers agree
        if len(results) > 1:
            agreeing_classifiers = sum(
                1 for r in results 
                if r.entity_type == best_result.entity_type and r.confidence > 0.5
            )
            agreement_boost = min(0.2, agreeing_classifiers * 0.1)
            signal_strength += agreement_boost
        
        # Boost if reasoning is detailed
        if best_result.reasoning and len(best_result.reasoning) > 50:
            signal_strength += 0.1
        
        # Boost if alternative types are provided (shows thorough analysis)
        if best_result.alternative_types:
            signal_strength += 0.05
        
        return min(1.0, signal_strength)
    
    def _calculate_content_quality(self, content_data: Optional[Dict]) -> float:
        """
        Calculate quality of content data for classification.
        
        Args:
            content_data: Content analysis data
            
        Returns:
            Content quality score between 0.0 and 1.0
        """
        if not content_data or "error" in content_data:
            return 0.3  # Low but not zero for missing content
        
        quality_score = 0.0
        
        # Check for title
        if content_data.get("title"):
            quality_score += 0.2
        
        # Check for description
        if content_data.get("description"):
            quality_score += 0.2
        
        # Check for content text
        content_text = content_data.get("content_text", "")
        if content_text:
            # Score based on content length
            text_length = len(content_text)
            if text_length > 1000:
                quality_score += 0.3
            elif text_length > 500:
                quality_score += 0.2
            elif text_length > 100:
                quality_score += 0.1
        
        # Check for structured data
        if content_data.get("meta_data"):
            quality_score += 0.1
        
        # Check for features
        if content_data.get("features"):
            quality_score += 0.1
        
        # Check for social links
        if content_data.get("social_links"):
            quality_score += 0.1
        
        return min(1.0, quality_score)
    
    def _calculate_url_reliability(self, entity: MinimalEntity) -> float:
        """
        Calculate reliability score based on URL characteristics.
        
        Args:
            entity: Entity to analyze
            
        Returns:
            URL reliability score between 0.0 and 1.0
        """
        url = str(entity.url).lower()
        reliability_score = 0.5  # Base score
        
        # Boost for HTTPS
        if url.startswith("https://"):
            reliability_score += 0.1
        
        # Boost for well-known domains
        well_known_domains = [
            "github.com", "gitlab.com", "huggingface.co", "arxiv.org",
            "openai.com", "anthropic.com", "google.com", "microsoft.com",
            "aws.amazon.com", "azure.microsoft.com", "cloud.google.com"
        ]
        
        for domain in well_known_domains:
            if domain in url:
                reliability_score += 0.2
                break
        
        # Boost for academic domains
        if any(tld in url for tld in [".edu", ".ac.", ".org"]):
            reliability_score += 0.15
        
        # Penalize for suspicious patterns
        suspicious_patterns = ["bit.ly", "tinyurl", "t.co", "short"]
        for pattern in suspicious_patterns:
            if pattern in url:
                reliability_score -= 0.2
                break
        
        # Boost for descriptive URLs
        if any(keyword in url for keyword in ["ai", "ml", "tool", "platform", "api"]):
            reliability_score += 0.05
        
        return max(0.0, min(1.0, reliability_score))
    
    def _calculate_name_clarity(self, entity: MinimalEntity) -> float:
        """
        Calculate clarity score based on entity name.
        
        Args:
            entity: Entity to analyze
            
        Returns:
            Name clarity score between 0.0 and 1.0
        """
        name = entity.name.lower()
        clarity_score = 0.5  # Base score
        
        # Boost for descriptive names
        descriptive_keywords = [
            "ai", "ml", "tool", "platform", "api", "app", "software",
            "course", "tutorial", "guide", "book", "paper", "research",
            "dataset", "model", "framework", "library"
        ]
        
        for keyword in descriptive_keywords:
            if keyword in name:
                clarity_score += 0.1
                break
        
        # Boost for proper capitalization
        if entity.name != entity.name.lower() and entity.name != entity.name.upper():
            clarity_score += 0.1
        
        # Penalize for very short or very long names
        name_length = len(entity.name)
        if name_length < 3:
            clarity_score -= 0.2
        elif name_length > 100:
            clarity_score -= 0.1
        elif 10 <= name_length <= 50:
            clarity_score += 0.1
        
        # Boost for brand-like names (mixed case, no spaces)
        if " " not in entity.name and any(c.isupper() for c in entity.name):
            clarity_score += 0.05
        
        return max(0.0, min(1.0, clarity_score))
    
    def _calibrate_confidence(self, raw_confidence: float, factors: Dict[str, float]) -> float:
        """
        Calibrate confidence score based on various factors.
        
        Args:
            raw_confidence: Raw weighted confidence score
            factors: Individual factor scores
            
        Returns:
            Calibrated confidence score
        """
        calibrated = raw_confidence
        
        # Apply sigmoid-like calibration to avoid extreme values
        calibrated = 1 / (1 + math.exp(-5 * (calibrated - 0.5)))
        
        # Adjust based on factor variance
        factor_values = list(factors.values())
        if factor_values:
            factor_variance = sum((f - raw_confidence) ** 2 for f in factor_values) / len(factor_values)
            
            # High variance indicates uncertainty
            if factor_variance > 0.1:
                calibrated *= 0.9
            elif factor_variance < 0.05:
                calibrated *= 1.05
        
        # Conservative adjustment for low-quality signals
        if factors.get("content_quality", 0) < 0.3:
            calibrated *= 0.8
        
        if factors.get("url_reliability", 0) < 0.4:
            calibrated *= 0.9
        
        return calibrated
    
    def get_confidence_level(self, confidence: float) -> str:
        """
        Get confidence level description.
        
        Args:
            confidence: Confidence score
            
        Returns:
            Confidence level string
        """
        if confidence >= self.thresholds["high_confidence"]:
            return "high"
        elif confidence >= self.thresholds["medium_confidence"]:
            return "medium"
        elif confidence >= self.thresholds["low_confidence"]:
            return "low"
        else:
            return "very_low"
    
    def should_use_llm(self, confidence: float) -> bool:
        """
        Determine if LLM classification should be used based on confidence.
        
        Args:
            confidence: Current confidence score
            
        Returns:
            True if LLM should be used, False otherwise
        """
        return confidence < self.thresholds["medium_confidence"]
    
    def get_confidence_explanation(self, confidence: float, factors: Dict[str, float]) -> str:
        """
        Generate human-readable explanation of confidence score.
        
        Args:
            confidence: Overall confidence score
            factors: Individual factor scores
            
        Returns:
            Confidence explanation string
        """
        level = self.get_confidence_level(confidence)
        
        # Find strongest and weakest factors
        strongest_factor = max(factors.keys(), key=lambda k: factors[k])
        weakest_factor = min(factors.keys(), key=lambda k: factors[k])
        
        explanation = f"Confidence: {level} ({confidence:.2f}). "
        explanation += f"Strongest signal: {strongest_factor} ({factors[strongest_factor]:.2f}). "
        
        if factors[weakest_factor] < 0.5:
            explanation += f"Weakest signal: {weakest_factor} ({factors[weakest_factor]:.2f}). "
        
        if level == "low" or level == "very_low":
            explanation += "Consider manual review or LLM classification."
        
        return explanation


# Global instance
confidence_scorer = ConfidenceScorer()


def get_confidence_scorer() -> ConfidenceScorer:
    """Get the global confidence scorer instance."""
    return confidence_scorer
