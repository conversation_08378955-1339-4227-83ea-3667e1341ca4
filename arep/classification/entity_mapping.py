"""
Entity type mapping system for the AI Resource Enhancement Pipeline.
Maps entity type names to their corresponding API UUIDs and provides metadata.
"""

from typing import Dict, List, Optional
from uuid import UUID

from arep.utils.logger import get_logger

logger = get_logger(__name__)


class EntityTypeMapping:
    """
    Manages mapping between entity type names and their API UUIDs.
    Provides metadata and validation for entity types.
    """
    
    def __init__(self):
        """Initialize the entity type mapping."""
        # Entity type mappings from the API documentation
        # These UUIDs should match the actual API entity types
        self.mappings = {
            "tool": {
                "uuid": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",  # Placeholder - replace with actual
                "name": "AI Tool",
                "description": "AI/ML tools, platforms, applications, APIs, or software services",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["logo_url", "pricing_model", "key_features"],
                "detail_type": "tool_details"
            },
            "course": {
                "uuid": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
                "name": "Course",
                "description": "Educational courses, tutorials, training programs, or learning materials",
                "category": "education",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["instructor_name", "duration_text", "skill_level"],
                "detail_type": "course_details"
            },
            "agency": {
                "uuid": "c3d4e5f6-g7h8-9012-cdef-************",
                "name": "Agency",
                "description": "Consulting agencies, service providers, or professional services companies",
                "category": "business",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["services_offered", "industry_focus", "location_summary"],
                "detail_type": "agency_details"
            },
            "content_creator": {
                "uuid": "d4e5f6g7-h8i9-0123-defg-************",
                "name": "Content Creator",
                "description": "Individual bloggers, writers, influencers, or content creators",
                "category": "content",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["creator_name", "primary_platform", "focus_areas"],
                "detail_type": "content_creator_details"
            },
            "community": {
                "uuid": "e5f6g7h8-i9j0-1234-efgh-************",
                "name": "Community",
                "description": "Online communities, forums, Discord servers, or social groups",
                "category": "social",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["platform", "member_count", "focus_topics"],
                "detail_type": "community_details"
            },
            "newsletter": {
                "uuid": "f6g7h8i9-j0k1-2345-fghi-************",
                "name": "Newsletter",
                "description": "Email newsletters, weekly digests, or subscription-based content",
                "category": "content",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["frequency", "main_topics", "author_name"],
                "detail_type": "newsletter_details"
            },
            "dataset": {
                "uuid": "g7h8i9j0-k1l2-3456-ghij-************",
                "name": "Dataset",
                "description": "Data collections, datasets, or data repositories",
                "category": "data",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["format", "source_url", "license"],
                "detail_type": "dataset_details"
            },
            "research_paper": {
                "uuid": "h8i9j0k1-l2m3-4567-hijk-************",
                "name": "Research Paper",
                "description": "Academic papers, research studies, or scientific publications",
                "category": "research",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["publication_date", "authors", "doi"],
                "detail_type": "research_paper_details"
            },
            "software": {
                "uuid": "i9j0k1l2-m3n4-5678-ijkl-************",
                "name": "Software",
                "description": "Open source software, libraries, frameworks, or development tools",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["repository_url", "license_type", "programming_languages"],
                "detail_type": "software_details"
            },
            "model": {
                "uuid": "j0k1l2m3-n4o5-6789-jklm-012345678901",
                "name": "AI Model",
                "description": "AI/ML models, checkpoints, or pre-trained models",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["model_architecture", "parameters_count", "training_dataset"],
                "detail_type": "model_details"
            },
            "project_reference": {
                "uuid": "k1l2m3n4-o5p6-7890-klmn-123456789012",
                "name": "Project Reference",
                "description": "Example projects, demos, or reference implementations",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["project_status", "source_code_url", "technologies"],
                "detail_type": "project_reference_details"
            },
            "service_provider": {
                "uuid": "l2m3n4o5-p6q7-8901-lmno-************",
                "name": "Service Provider",
                "description": "Cloud services, hosting providers, or infrastructure services",
                "category": "business",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["service_areas", "case_studies_url", "industry_specializations"],
                "detail_type": "service_provider_details"
            },
            "investor": {
                "uuid": "m3n4o5p6-q7r8-9012-mnop-************",
                "name": "Investor",
                "description": "Venture capital firms, investors, or funding organizations",
                "category": "finance",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["investment_focus_areas", "typical_investment_size", "investment_stages"],
                "detail_type": "investor_details"
            },
            "event": {
                "uuid": "n4o5p6q7-r8s9-0123-nopq-************",
                "name": "Event",
                "description": "Conferences, meetups, workshops, or industry events",
                "category": "event",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["event_type", "start_date", "location"],
                "detail_type": "event_details"
            },
            "job": {
                "uuid": "o5p6q7r8-s9t0-1234-opqr-************",
                "name": "Job",
                "description": "Job postings, career opportunities, or recruitment listings",
                "category": "career",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["company_name", "employment_types", "location"],
                "detail_type": "job_details"
            },
            "grant": {
                "uuid": "p6q7r8s9-t0u1-2345-pqrs-************",
                "name": "Grant",
                "description": "Grants, funding opportunities, or financial awards",
                "category": "finance",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["granting_institution", "funding_amount", "application_deadline"],
                "detail_type": "grant_details"
            },
            "bounty": {
                "uuid": "q7r8s9t0-u1v2-3456-qrst-************",
                "name": "Bounty",
                "description": "Bug bounties, challenges, or reward programs",
                "category": "finance",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["bounty_issuer", "reward_amount", "requirements"],
                "detail_type": "bounty_details"
            },
            "hardware": {
                "uuid": "r8s9t0u1-v2w3-4567-rstu-************",
                "name": "Hardware",
                "description": "Computer hardware, GPUs, processors, or physical devices",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["hardware_type", "manufacturer", "specifications"],
                "detail_type": "hardware_details"
            },
            "news": {
                "uuid": "s9t0u1v2-w3x4-5678-stuv-************",
                "name": "News",
                "description": "News articles, press releases, or journalism content",
                "category": "content",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["publication_date", "source_name", "author"],
                "detail_type": "news_details"
            },
            "book": {
                "uuid": "t0u1v2w3-x4y5-6789-tuvw-012345678901",
                "name": "Book",
                "description": "Books, ebooks, guides, or published literature",
                "category": "content",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["author_names", "isbn", "publisher"],
                "detail_type": "book_details"
            },
            "podcast": {
                "uuid": "u1v2w3x4-y5z6-7890-uvwx-123456789012",
                "name": "Podcast",
                "description": "Podcasts, audio content, or interview series",
                "category": "content",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["host_names", "average_episode_length", "main_topics"],
                "detail_type": "podcast_details"
            },
            "platform": {
                "uuid": "v2w3x4y5-z6a7-8901-vwxy-************",
                "name": "Platform",
                "description": "Platforms, marketplaces, or multi-service ecosystems",
                "category": "technology",
                "requires_fields": ["name", "website_url", "short_description"],
                "optional_fields": ["platform_type", "key_services", "supported_regions"],
                "detail_type": "platform_details"
            }
        }
    
    def get_uuid(self, entity_type: str) -> Optional[UUID]:
        """
        Get UUID for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            UUID object or None if not found
        """
        mapping = self.mappings.get(entity_type.lower())
        if mapping:
            try:
                return UUID(mapping["uuid"])
            except ValueError:
                logger.error(f"Invalid UUID for entity type {entity_type}: {mapping['uuid']}")
                return None
        return None
    
    def get_mapping(self, entity_type: str) -> Optional[Dict]:
        """
        Get complete mapping information for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            Mapping dictionary or None if not found
        """
        return self.mappings.get(entity_type.lower())
    
    def get_all_types(self) -> List[str]:
        """Get list of all available entity types."""
        return list(self.mappings.keys())
    
    def get_types_by_category(self, category: str) -> List[str]:
        """
        Get entity types by category.
        
        Args:
            category: Category name
            
        Returns:
            List of entity types in the category
        """
        return [
            entity_type for entity_type, mapping in self.mappings.items()
            if mapping["category"] == category.lower()
        ]
    
    def get_categories(self) -> List[str]:
        """Get list of all categories."""
        categories = set()
        for mapping in self.mappings.values():
            categories.add(mapping["category"])
        return sorted(list(categories))
    
    def validate_entity_type(self, entity_type: str) -> bool:
        """
        Validate if an entity type exists.
        
        Args:
            entity_type: Entity type to validate
            
        Returns:
            True if valid, False otherwise
        """
        return entity_type.lower() in self.mappings
    
    def get_required_fields(self, entity_type: str) -> List[str]:
        """
        Get required fields for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            List of required field names
        """
        mapping = self.mappings.get(entity_type.lower())
        return mapping["requires_fields"] if mapping else []
    
    def get_optional_fields(self, entity_type: str) -> List[str]:
        """
        Get optional fields for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            List of optional field names
        """
        mapping = self.mappings.get(entity_type.lower())
        return mapping["optional_fields"] if mapping else []
    
    def get_detail_type(self, entity_type: str) -> Optional[str]:
        """
        Get the detail type field name for an entity type.
        
        Args:
            entity_type: Entity type name
            
        Returns:
            Detail type field name or None
        """
        mapping = self.mappings.get(entity_type.lower())
        return mapping["detail_type"] if mapping else None


# Global instance
entity_type_mapping = EntityTypeMapping()


def get_entity_type_mapping() -> EntityTypeMapping:
    """Get the global entity type mapping instance."""
    return entity_type_mapping
