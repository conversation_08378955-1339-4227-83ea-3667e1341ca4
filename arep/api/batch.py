"""
Batch processing utilities for efficient resource submission to the AI Navigator API.
Provides advanced batching strategies, parallel processing, and progress tracking.
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union
from uuid import uuid4

from arep.utils.logger import get_logger

from .client import AINavigatorClient
from .models import (BatchSubmissionResponse, ProcessingMetrics, Resource,
                     ResourceSubmissionResponse)
from .rate_limiter import AdaptiveRateLimiter, create_rate_limiter

logger = get_logger(__name__)


class BatchStrategy(Enum):
    """Batch processing strategies."""

    SEQUENTIAL = "sequential"  # Process one at a time
    PARALLEL = "parallel"  # Process all in parallel
    CHUNKED = "chunked"  # Process in chunks
    ADAPTIVE = "adaptive"  # Adapt based on performance


@dataclass
class BatchConfig:
    """Configuration for batch processing."""

    batch_size: int = 10
    max_concurrent: int = 5
    delay_between_batches: float = 1.0
    strategy: BatchStrategy = BatchStrategy.CHUNKED
    retry_failed: bool = True
    max_retries: int = 3
    progress_callback: Optional[Callable] = None
    validate_before_submit: bool = True


@dataclass
class BatchProgress:
    """Progress tracking for batch operations."""

    batch_id: str = field(default_factory=lambda: str(uuid4()))
    total_items: int = 0
    processed_items: int = 0
    successful_items: int = 0
    failed_items: int = 0
    current_batch: int = 0
    total_batches: int = 0
    start_time: float = field(default_factory=time.time)
    end_time: Optional[float] = None

    @property
    def progress_percentage(self) -> float:
        """Calculate progress percentage."""
        if self.total_items == 0:
            return 0.0
        return (self.processed_items / self.total_items) * 100

    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.processed_items == 0:
            return 0.0
        return (self.successful_items / self.processed_items) * 100

    @property
    def elapsed_time(self) -> float:
        """Calculate elapsed time."""
        end = self.end_time or time.time()
        return end - self.start_time

    @property
    def estimated_time_remaining(self) -> Optional[float]:
        """Estimate remaining time based on current progress."""
        if self.processed_items == 0 or self.progress_percentage >= 100:
            return None

        avg_time_per_item = self.elapsed_time / self.processed_items
        remaining_items = self.total_items - self.processed_items
        return avg_time_per_item * remaining_items


class BatchProcessor:
    """
    Advanced batch processor for resource submissions.
    Supports multiple processing strategies and comprehensive progress tracking.
    """

    def __init__(
        self,
        client: AINavigatorClient,
        config: Optional[BatchConfig] = None,
        rate_limiter: Optional[AdaptiveRateLimiter] = None,
    ):
        """
        Initialize batch processor.

        Args:
            client: AI Navigator API client
            config: Batch processing configuration
            rate_limiter: Rate limiter for API requests
        """
        self.client = client
        self.config = config or BatchConfig()
        self.rate_limiter = rate_limiter or create_rate_limiter()

        # Processing state
        self.active_batches: Dict[str, BatchProgress] = {}
        self.metrics = ProcessingMetrics()

    async def process_resources(
        self,
        resources: List[Resource],
        config: Optional[BatchConfig] = None,
    ) -> BatchSubmissionResponse:
        """
        Process a list of resources using the configured strategy.

        Args:
            resources: List of resources to process
            config: Override configuration for this batch

        Returns:
            BatchSubmissionResponse with results
        """
        batch_config = config or self.config
        progress = BatchProgress(
            total_items=len(resources),
            total_batches=self._calculate_total_batches(len(resources), batch_config),
        )

        self.active_batches[progress.batch_id] = progress

        logger.info(
            f"Starting batch processing of {len(resources)} resources "
            f"using {batch_config.strategy.value} strategy"
        )

        try:
            if batch_config.strategy == BatchStrategy.SEQUENTIAL:
                result = await self._process_sequential(
                    resources, batch_config, progress
                )
            elif batch_config.strategy == BatchStrategy.PARALLEL:
                result = await self._process_parallel(resources, batch_config, progress)
            elif batch_config.strategy == BatchStrategy.CHUNKED:
                result = await self._process_chunked(resources, batch_config, progress)
            elif batch_config.strategy == BatchStrategy.ADAPTIVE:
                result = await self._process_adaptive(resources, batch_config, progress)
            else:
                raise ValueError(f"Unknown batch strategy: {batch_config.strategy}")

            progress.end_time = time.time()

            # Update metrics
            self.metrics.total_processed += progress.processed_items
            self.metrics.successful_submissions += progress.successful_items
            self.metrics.failed_submissions += progress.failed_items

            logger.info(
                f"Batch processing completed: {progress.successful_items}/{progress.total_items} successful "
                f"in {progress.elapsed_time:.2f}s"
            )

            return result

        finally:
            # Clean up
            if progress.batch_id in self.active_batches:
                del self.active_batches[progress.batch_id]

    async def _process_sequential(
        self,
        resources: List[Resource],
        config: BatchConfig,
        progress: BatchProgress,
    ) -> BatchSubmissionResponse:
        """Process resources one at a time."""
        results = []

        for i, resource in enumerate(resources):
            if config.validate_before_submit:
                validation_errors = self._validate_resource(resource)
                if validation_errors:
                    logger.warning(
                        f"Validation failed for resource {resource.name}: {validation_errors}"
                    )
                    results.append(
                        self._create_validation_error_response(
                            resource, validation_errors
                        )
                    )
                    self._update_progress(progress, failed=True)
                    continue

            try:
                # Apply rate limiting
                await self.rate_limiter.acquire()

                # Submit resource
                result = await self.client.submit_resource(resource)
                results.append(result)

                if result.success:
                    self._update_progress(progress, success=True)
                    self.rate_limiter.record_success()
                else:
                    self._update_progress(progress, failed=True)

            except Exception as e:
                logger.error(f"Error processing resource {resource.name}: {e}")
                error_response = ResourceSubmissionResponse(
                    success=False,
                    message=str(e),
                )
                results.append(error_response)
                self._update_progress(progress, failed=True)

            # Call progress callback if provided
            if config.progress_callback:
                await self._call_progress_callback(config.progress_callback, progress)

        return self._create_batch_response(results, progress)

    async def _process_parallel(
        self,
        resources: List[Resource],
        config: BatchConfig,
        progress: BatchProgress,
    ) -> BatchSubmissionResponse:
        """Process all resources in parallel with concurrency limit."""
        semaphore = asyncio.Semaphore(config.max_concurrent)

        async def process_single_resource(
            resource: Resource,
        ) -> ResourceSubmissionResponse:
            async with semaphore:
                if config.validate_before_submit:
                    validation_errors = self._validate_resource(resource)
                    if validation_errors:
                        self._update_progress(progress, failed=True)
                        return self._create_validation_error_response(
                            resource, validation_errors
                        )

                try:
                    await self.rate_limiter.acquire()
                    result = await self.client.submit_resource(resource)

                    if result.success:
                        self._update_progress(progress, success=True)
                        self.rate_limiter.record_success()
                    else:
                        self._update_progress(progress, failed=True)

                    return result

                except Exception as e:
                    logger.error(f"Error processing resource {resource.name}: {e}")
                    self._update_progress(progress, failed=True)
                    return ResourceSubmissionResponse(
                        success=False,
                        message=str(e),
                    )

        # Process all resources in parallel
        tasks = [process_single_resource(resource) for resource in resources]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Handle any exceptions that weren't caught
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                processed_results.append(
                    ResourceSubmissionResponse(
                        success=False,
                        message=str(result),
                    )
                )
            else:
                processed_results.append(result)

        return self._create_batch_response(processed_results, progress)

    async def _process_chunked(
        self,
        resources: List[Resource],
        config: BatchConfig,
        progress: BatchProgress,
    ) -> BatchSubmissionResponse:
        """Process resources in chunks."""
        all_results = []

        for i in range(0, len(resources), config.batch_size):
            chunk = resources[i : i + config.batch_size]
            progress.current_batch = (i // config.batch_size) + 1

            logger.debug(
                f"Processing chunk {progress.current_batch}/{progress.total_batches}"
            )

            # Process chunk in parallel
            chunk_config = BatchConfig(
                strategy=BatchStrategy.PARALLEL,
                max_concurrent=config.max_concurrent,
                validate_before_submit=config.validate_before_submit,
            )

            chunk_results = await self._process_parallel(chunk, chunk_config, progress)
            all_results.extend(chunk_results.submission_results)

            # Delay between chunks (except for the last one)
            if i + config.batch_size < len(resources):
                await asyncio.sleep(config.delay_between_batches)

            # Call progress callback if provided
            if config.progress_callback:
                await self._call_progress_callback(config.progress_callback, progress)

        return self._create_batch_response(all_results, progress)

    async def _process_adaptive(
        self,
        resources: List[Resource],
        config: BatchConfig,
        progress: BatchProgress,
    ) -> BatchSubmissionResponse:
        """Process resources with adaptive strategy based on performance."""
        # Start with chunked strategy
        current_strategy = BatchStrategy.CHUNKED
        current_batch_size = config.batch_size

        all_results = []
        performance_window = []

        for i in range(0, len(resources), current_batch_size):
            chunk = resources[i : i + current_batch_size]
            chunk_start_time = time.time()

            # Process chunk
            if current_strategy == BatchStrategy.CHUNKED:
                chunk_config = BatchConfig(
                    strategy=BatchStrategy.PARALLEL,
                    max_concurrent=config.max_concurrent,
                    batch_size=current_batch_size,
                )
                chunk_results = await self._process_parallel(
                    chunk, chunk_config, progress
                )
            else:
                chunk_results = await self._process_sequential(chunk, config, progress)

            chunk_time = time.time() - chunk_start_time
            chunk_success_rate = sum(
                1 for r in chunk_results.submission_results if r.success
            ) / len(chunk)

            all_results.extend(chunk_results.submission_results)
            performance_window.append((chunk_time, chunk_success_rate, len(chunk)))

            # Adapt strategy based on recent performance
            if len(performance_window) >= 3:
                current_strategy, current_batch_size = self._adapt_strategy(
                    performance_window, current_batch_size
                )
                performance_window = performance_window[-3:]  # Keep only recent data

            # Delay between chunks
            if i + current_batch_size < len(resources):
                await asyncio.sleep(config.delay_between_batches)

        return self._create_batch_response(all_results, progress)

    def _adapt_strategy(
        self, performance_window: List, current_batch_size: int
    ) -> tuple:
        """Adapt processing strategy based on performance metrics."""
        avg_time = sum(p[0] for p in performance_window) / len(performance_window)
        avg_success_rate = sum(p[1] for p in performance_window) / len(
            performance_window
        )

        # If success rate is low, reduce batch size
        if avg_success_rate < 0.8:
            new_batch_size = max(1, current_batch_size // 2)
            logger.info(
                f"Reducing batch size to {new_batch_size} due to low success rate"
            )
            return BatchStrategy.CHUNKED, new_batch_size

        # If performance is good, try to increase batch size
        elif avg_success_rate > 0.95 and avg_time < 5.0:
            new_batch_size = min(50, current_batch_size * 2)
            logger.info(
                f"Increasing batch size to {new_batch_size} due to good performance"
            )
            return BatchStrategy.CHUNKED, new_batch_size

        return BatchStrategy.CHUNKED, current_batch_size

    def _calculate_total_batches(self, total_items: int, config: BatchConfig) -> int:
        """Calculate total number of batches."""
        if config.strategy in [BatchStrategy.SEQUENTIAL, BatchStrategy.PARALLEL]:
            return 1
        return (total_items + config.batch_size - 1) // config.batch_size

    def _validate_resource(self, resource: Resource) -> List[str]:
        """Validate a resource before submission."""
        from .validation import ResponseValidator

        return ResponseValidator.validate_resource_data(resource)

    def _create_validation_error_response(
        self, resource: Resource, errors: List[str]
    ) -> ResourceSubmissionResponse:
        """Create an error response for validation failures."""
        return ResourceSubmissionResponse(
            success=False,
            message=f"Validation failed for {resource.name}: {'; '.join(errors)}",
        )

    def _update_progress(
        self, progress: BatchProgress, success: bool = False, failed: bool = False
    ):
        """Update progress tracking."""
        progress.processed_items += 1
        if success:
            progress.successful_items += 1
        elif failed:
            progress.failed_items += 1

    async def _call_progress_callback(
        self, callback: Callable, progress: BatchProgress
    ):
        """Call progress callback safely."""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(progress)
            else:
                callback(progress)
        except Exception as e:
            logger.warning(f"Progress callback failed: {e}")

    def _create_batch_response(
        self, results: List[ResourceSubmissionResponse], progress: BatchProgress
    ) -> BatchSubmissionResponse:
        """Create final batch response."""
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful

        return BatchSubmissionResponse(
            success=failed == 0,
            message=f"Batch processing completed: {successful}/{len(results)} successful",
            total_submitted=len(results),
            successful_submissions=successful,
            failed_submissions=failed,
            submission_results=results,
        )

    def get_active_batches(self) -> Dict[str, BatchProgress]:
        """Get currently active batch operations."""
        return self.active_batches.copy()

    def get_metrics(self) -> ProcessingMetrics:
        """Get overall processing metrics."""
        return self.metrics
