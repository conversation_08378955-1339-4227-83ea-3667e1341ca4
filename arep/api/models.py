"""
Core data models for the AI Resource Enhancement Pipeline.
Defines Pydantic models for API requests, responses, and internal data structures.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, HttpUrl


class SocialLinks(BaseModel):
    """Social media links for an entity."""

    twitter: Optional[str] = None
    linkedin: Optional[str] = None


class PerformanceMetrics(BaseModel):
    """Performance metrics for models."""

    accuracy: Optional[float] = None
    f1_score: Optional[float] = None


class Specifications(BaseModel):
    """Hardware specifications."""

    memory: Optional[str] = None
    cuda_cores: Optional[int] = None
    tflops: Optional[float] = None
    architecture: Optional[str] = None


class Contributor(BaseModel):
    """Project contributor information."""

    name: str
    role: Optional[str] = None


class ToolDetails(BaseModel):
    """Details specific to AI tools."""

    learning_curve: Optional[str] = None
    key_features: List[str] = Field(default_factory=list)
    has_free_tier: Optional[bool] = None
    use_cases: List[str] = Field(default_factory=list)
    pricing_model: Optional[str] = None
    price_range: Optional[str] = None
    pricing_details: Optional[str] = None
    pricing_url: Optional[HttpUrl] = None
    integrations: List[str] = Field(default_factory=list)
    support_email: Optional[str] = None
    has_live_chat: Optional[bool] = None
    community_url: Optional[HttpUrl] = None
    programming_languages: List[str] = Field(default_factory=list)
    frameworks: List[str] = Field(default_factory=list)
    libraries: List[str] = Field(default_factory=list)
    target_audience: List[str] = Field(default_factory=list)
    deployment_options: List[str] = Field(default_factory=list)
    supported_os: List[str] = Field(default_factory=list)
    mobile_support: Optional[bool] = None
    api_access: Optional[bool] = None
    customization_level: Optional[str] = None
    trial_available: Optional[bool] = None
    demo_available: Optional[bool] = None
    open_source: Optional[bool] = None
    support_channels: List[str] = Field(default_factory=list)
    technical_level: Optional[str] = None
    platforms: List[str] = Field(default_factory=list)
    has_api: Optional[bool] = None
    api_documentation_url: Optional[HttpUrl] = None


class CourseDetails(BaseModel):
    """Details specific to courses."""

    instructor_name: Optional[str] = None
    duration_text: Optional[str] = None
    skill_level: Optional[str] = None
    prerequisites: Optional[str] = None
    syllabus_url: Optional[HttpUrl] = None
    enrollment_count: Optional[int] = None
    certificate_available: Optional[bool] = None


class AgencyDetails(BaseModel):
    """Details specific to agencies."""

    services_offered: List[str] = Field(default_factory=list)
    industry_focus: List[str] = Field(default_factory=list)
    target_client_size: List[str] = Field(default_factory=list)
    target_audience: List[str] = Field(default_factory=list)
    location_summary: Optional[str] = None
    portfolio_url: Optional[HttpUrl] = None
    pricing_info: Optional[str] = None


class ContentCreatorDetails(BaseModel):
    """Details specific to content creators."""

    creator_name: Optional[str] = None
    primary_platform: Optional[str] = None
    focus_areas: List[str] = Field(default_factory=list)
    follower_count: Optional[int] = None
    example_content_url: Optional[HttpUrl] = None


class CommunityDetails(BaseModel):
    """Details specific to communities."""

    platform: Optional[str] = None
    member_count: Optional[int] = None
    focus_topics: List[str] = Field(default_factory=list)
    rules_url: Optional[HttpUrl] = None
    invite_url: Optional[HttpUrl] = None
    main_channel_url: Optional[HttpUrl] = None


class NewsletterDetails(BaseModel):
    """Details specific to newsletters."""

    frequency: Optional[str] = None
    main_topics: List[str] = Field(default_factory=list)
    archive_url: Optional[HttpUrl] = None
    subscribe_url: Optional[HttpUrl] = None
    author_name: Optional[str] = None
    subscriber_count: Optional[int] = None


class DatasetDetails(BaseModel):
    """Details specific to datasets."""

    format: Optional[str] = None
    source_url: Optional[HttpUrl] = None
    license: Optional[str] = None
    size_in_bytes: Optional[int] = None
    description: Optional[str] = None
    access_notes: Optional[str] = None


class ResearchPaperDetails(BaseModel):
    """Details specific to research papers."""

    publication_date: Optional[str] = None
    doi: Optional[str] = None
    authors: List[str] = Field(default_factory=list)
    research_areas: List[str] = Field(default_factory=list)
    publication_venues: List[str] = Field(default_factory=list)
    keywords: List[str] = Field(default_factory=list)
    arxiv_id: Optional[str] = None
    abstract: Optional[str] = None
    journal_or_conference: Optional[str] = None
    pdf_url: Optional[HttpUrl] = None
    citation_count: Optional[int] = None


class SoftwareDetails(BaseModel):
    """Details specific to software."""

    repository_url: Optional[HttpUrl] = None
    license_type: Optional[str] = None
    programming_languages: List[str] = Field(default_factory=list)
    platform_compatibility: List[str] = Field(default_factory=list)
    current_version: Optional[str] = None
    release_date: Optional[str] = None
    open_source: Optional[bool] = None
    has_free_tier: Optional[bool] = None
    use_cases: List[str] = Field(default_factory=list)
    pricing_model: Optional[str] = None
    price_range: Optional[str] = None
    pricing_details: Optional[str] = None
    pricing_url: Optional[HttpUrl] = None
    integrations: List[str] = Field(default_factory=list)
    support_email: Optional[str] = None
    has_live_chat: Optional[bool] = None
    community_url: Optional[HttpUrl] = None
    api_access: Optional[bool] = None
    customization_level: Optional[str] = None
    demo_available: Optional[bool] = None
    deployment_options: List[str] = Field(default_factory=list)
    frameworks: List[str] = Field(default_factory=list)
    has_api: Optional[bool] = None
    key_features: List[str] = Field(default_factory=list)
    libraries: List[str] = Field(default_factory=list)
    mobile_support: Optional[bool] = None
    support_channels: List[str] = Field(default_factory=list)
    supported_os: List[str] = Field(default_factory=list)
    target_audience: List[str] = Field(default_factory=list)
    trial_available: Optional[bool] = None


class ModelDetails(BaseModel):
    """Details specific to AI models."""

    model_architecture: Optional[str] = None
    parameters_count: Optional[int] = None
    training_dataset: Optional[str] = None
    performance_metrics: Optional[PerformanceMetrics] = None
    model_url: Optional[HttpUrl] = None
    license: Optional[str] = None


class ProjectReferenceDetails(BaseModel):
    """Details specific to project references."""

    project_status: Optional[str] = None
    source_code_url: Optional[HttpUrl] = None
    live_demo_url: Optional[HttpUrl] = None
    technologies: List[str] = Field(default_factory=list)
    project_goals: Optional[str] = None
    contributors: List[Union[str, Contributor]] = Field(default_factory=list)


class ServiceProviderDetails(BaseModel):
    """Details specific to service providers."""

    service_areas: List[str] = Field(default_factory=list)
    case_studies_url: Optional[HttpUrl] = None
    consultation_booking_url: Optional[HttpUrl] = None
    industry_specializations: List[str] = Field(default_factory=list)
    company_size_focus: Optional[str] = None
    hourly_rate_range: Optional[str] = None


class InvestorDetails(BaseModel):
    """Details specific to investors."""

    investment_focus_areas: List[str] = Field(default_factory=list)
    portfolio_url: Optional[HttpUrl] = None
    typical_investment_size: Optional[str] = None
    investment_stages: List[str] = Field(default_factory=list)
    contact_email: Optional[str] = None
    preferred_communication: Optional[str] = None


class EventDetails(BaseModel):
    """Details specific to events."""

    event_type: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    location: Optional[str] = None
    is_online: Optional[bool] = None
    event_format: Optional[str] = None
    registration_required: Optional[bool] = None
    registration_url: Optional[HttpUrl] = None
    capacity: Optional[int] = None
    organizer: Optional[str] = None
    key_speakers: List[str] = Field(default_factory=list)
    target_audience: List[str] = Field(default_factory=list)
    topics: List[str] = Field(default_factory=list)
    price: Optional[str] = None


class JobDetails(BaseModel):
    """Details specific to job postings."""

    company_name: Optional[str] = None
    employment_types: List[str] = Field(default_factory=list)
    experience_level: Optional[str] = None
    location_types: List[str] = Field(default_factory=list)
    salary_min: Optional[int] = None
    salary_max: Optional[int] = None
    application_url: Optional[HttpUrl] = None
    job_description: Optional[str] = None
    is_remote: Optional[bool] = None
    location: Optional[str] = None
    job_type: Optional[str] = None
    key_responsibilities: List[str] = Field(default_factory=list)
    required_skills: List[str] = Field(default_factory=list)
    benefits: List[str] = Field(default_factory=list)
    remote_policy: Optional[str] = None
    visa_sponsorship: Optional[bool] = None


class GrantDetails(BaseModel):
    """Details specific to grants."""

    granting_institution: Optional[str] = None
    eligibility_criteria: Optional[str] = None
    application_deadline: Optional[str] = None
    funding_amount: Optional[str] = None
    application_url: Optional[HttpUrl] = None
    grant_focus_area: Optional[str] = None


class BountyDetails(BaseModel):
    """Details specific to bounties."""

    bounty_issuer: Optional[str] = None
    reward_amount: Optional[str] = None
    requirements: Optional[str] = None
    submission_deadline: Optional[str] = None
    platform_url: Optional[HttpUrl] = None
    difficulty_level: Optional[str] = None


class HardwareDetails(BaseModel):
    """Details specific to hardware."""

    hardware_type: Optional[str] = None
    manufacturer: Optional[str] = None
    release_date: Optional[str] = None
    specifications: Optional[Specifications] = None
    datasheet_url: Optional[HttpUrl] = None
    memory: Optional[str] = None
    processor: Optional[str] = None
    storage: Optional[str] = None
    power_consumption: Optional[str] = None
    availability: Optional[str] = None
    price: Optional[str] = None
    gpu: Optional[str] = None
    use_cases: List[str] = Field(default_factory=list)


class NewsDetails(BaseModel):
    """Details specific to news articles."""

    publication_date: Optional[str] = None
    source_name: Optional[str] = None
    articleUrl: Optional[HttpUrl] = None
    author: Optional[str] = None
    summary: Optional[str] = None
    status: Optional[str] = None


class BookDetails(BaseModel):
    """Details specific to books."""

    author_names: List[str] = Field(default_factory=list)
    isbn: Optional[str] = None
    publisher: Optional[str] = None
    publication_year: Optional[int] = None
    page_count: Optional[int] = None
    summary: Optional[str] = None
    purchase_url: Optional[HttpUrl] = None


class PodcastDetails(BaseModel):
    """Details specific to podcasts."""

    host_names: List[str] = Field(default_factory=list)
    average_episode_length: Optional[str] = None
    main_topics: List[str] = Field(default_factory=list)
    listen_url: Optional[HttpUrl] = None
    frequency: Optional[str] = None
    primary_language: Optional[str] = None


class PlatformDetails(BaseModel):
    """Details specific to platforms."""

    platform_type: Optional[str] = None
    key_services: List[str] = Field(default_factory=list)
    documentation_url: Optional[HttpUrl] = None
    pricing_model: Optional[str] = None
    sla_url: Optional[HttpUrl] = None
    supported_regions: List[str] = Field(default_factory=list)
    has_free_tier: Optional[bool] = None
    use_cases: List[str] = Field(default_factory=list)
    price_range: Optional[str] = None
    pricing_details: Optional[str] = None
    pricing_url: Optional[HttpUrl] = None
    integrations: List[str] = Field(default_factory=list)
    support_email: Optional[str] = None
    has_live_chat: Optional[bool] = None
    community_url: Optional[HttpUrl] = None


# Main Resource Models


class Resource(BaseModel):
    """
    Main resource model for submitting entities to the AI Navigator API.
    This represents the complete structure for creating a new entity.
    """

    name: str = Field(..., description="Name of the resource")
    website_url: HttpUrl = Field(..., description="Main website URL")
    entity_type_id: UUID = Field(..., description="UUID of the entity type")
    short_description: str = Field(..., description="Brief description of the resource")
    description: Optional[str] = Field(None, description="Detailed description")
    logo_url: Optional[HttpUrl] = Field(None, description="URL to the logo image")
    documentation_url: Optional[HttpUrl] = Field(None, description="Documentation URL")
    contact_url: Optional[HttpUrl] = Field(None, description="Contact page URL")
    privacy_policy_url: Optional[HttpUrl] = Field(
        None, description="Privacy policy URL"
    )
    founded_year: Optional[int] = Field(None, description="Year the entity was founded")
    social_links: Optional[SocialLinks] = Field(None, description="Social media links")
    category_ids: List[UUID] = Field(
        default_factory=list, description="List of category UUIDs"
    )
    tag_ids: List[UUID] = Field(default_factory=list, description="List of tag UUIDs")
    feature_ids: List[UUID] = Field(
        default_factory=list, description="List of feature UUIDs"
    )
    meta_title: Optional[str] = Field(None, description="SEO meta title")
    meta_description: Optional[str] = Field(None, description="SEO meta description")
    employee_count_range: Optional[str] = Field(
        None, description="Employee count range"
    )
    funding_stage: Optional[str] = Field(None, description="Current funding stage")
    location_summary: Optional[str] = Field(None, description="Location summary")
    ref_link: Optional[HttpUrl] = Field(None, description="Referral link")
    affiliate_status: Optional[str] = Field(None, description="Affiliate status")
    scraped_review_sentiment_label: Optional[str] = Field(
        None, description="Review sentiment"
    )
    scraped_review_sentiment_score: Optional[float] = Field(
        None, description="Sentiment score"
    )
    scraped_review_count: Optional[int] = Field(None, description="Number of reviews")
    status: Optional[str] = Field(None, description="Entity status")

    # Type-specific details (only one should be populated based on entity type)
    tool_details: Optional[ToolDetails] = None
    course_details: Optional[CourseDetails] = None
    agency_details: Optional[AgencyDetails] = None
    content_creator_details: Optional[ContentCreatorDetails] = None
    community_details: Optional[CommunityDetails] = None
    newsletter_details: Optional[NewsletterDetails] = None
    dataset_details: Optional[DatasetDetails] = None
    research_paper_details: Optional[ResearchPaperDetails] = None
    software_details: Optional[SoftwareDetails] = None
    model_details: Optional[ModelDetails] = None
    project_reference_details: Optional[ProjectReferenceDetails] = None
    service_provider_details: Optional[ServiceProviderDetails] = None
    investor_details: Optional[InvestorDetails] = None
    event_details: Optional[EventDetails] = None
    job_details: Optional[JobDetails] = None
    grant_details: Optional[GrantDetails] = None
    bounty_details: Optional[BountyDetails] = None
    hardware_details: Optional[HardwareDetails] = None
    news_details: Optional[NewsDetails] = None
    book_details: Optional[BookDetails] = None
    podcast_details: Optional[PodcastDetails] = None
    platform_details: Optional[PlatformDetails] = None


class EnhancedResource(Resource):
    """
    Enhanced resource model that includes additional metadata and processing information.
    Used internally for tracking enhancement progress and results.
    """

    # Enhancement metadata
    enhancement_id: Optional[UUID] = Field(
        None, description="Unique enhancement process ID"
    )
    source_url: Optional[HttpUrl] = Field(
        None, description="Original source URL where found"
    )
    discovered_at: Optional[datetime] = Field(
        None, description="When the resource was discovered"
    )
    enhanced_at: Optional[datetime] = Field(
        None, description="When enhancement was completed"
    )
    enhancement_version: Optional[str] = Field(
        None, description="Version of enhancement process"
    )

    # Processing status
    processing_status: Optional[str] = Field(
        None, description="Current processing status"
    )
    processing_errors: List[str] = Field(
        default_factory=list, description="Any processing errors"
    )

    # Enhancement results
    ai_generated_description: Optional[str] = Field(
        None, description="AI-enhanced description"
    )
    extracted_features: List[str] = Field(
        default_factory=list, description="Auto-extracted features"
    )
    suggested_categories: List[str] = Field(
        default_factory=list, description="AI-suggested categories"
    )
    suggested_tags: List[str] = Field(
        default_factory=list, description="AI-suggested tags"
    )
    quality_score: Optional[float] = Field(None, description="Quality assessment score")

    # Submission tracking
    submitted_to_api: Optional[bool] = Field(
        False, description="Whether submitted to API"
    )
    api_submission_id: Optional[str] = Field(None, description="API submission ID")
    api_response_status: Optional[int] = Field(
        None, description="API response status code"
    )
    api_response_message: Optional[str] = Field(
        None, description="API response message"
    )
    submitted_at: Optional[datetime] = Field(None, description="When submitted to API")


# API Response Models


class APIError(BaseModel):
    """Standard API error response model."""

    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(
        None, description="Additional error details"
    )


class APIResponse(BaseModel):
    """Base API response model."""

    success: bool = Field(..., description="Whether the request was successful")
    message: Optional[str] = Field(None, description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    errors: Optional[List[APIError]] = Field(None, description="List of errors if any")


class ResourceSubmissionResponse(APIResponse):
    """Response model for resource submission."""

    entity_id: Optional[UUID] = Field(None, description="Created entity ID")
    status: Optional[str] = Field(None, description="Submission status")


class BatchSubmissionResponse(APIResponse):
    """Response model for batch resource submission."""

    total_submitted: int = Field(..., description="Total number of resources submitted")
    successful_submissions: int = Field(
        ..., description="Number of successful submissions"
    )
    failed_submissions: int = Field(..., description="Number of failed submissions")
    submission_results: List[ResourceSubmissionResponse] = Field(
        default_factory=list, description="Individual submission results"
    )


# Utility Models


class ProcessingMetrics(BaseModel):
    """Metrics for tracking processing performance."""

    total_processed: int = 0
    successful_submissions: int = 0
    failed_submissions: int = 0
    average_processing_time: Optional[float] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None


class RateLimitInfo(BaseModel):
    """Rate limiting information."""

    requests_per_minute: int = Field(..., description="Allowed requests per minute")
    requests_remaining: int = Field(
        ..., description="Remaining requests in current window"
    )
    reset_time: datetime = Field(..., description="When the rate limit resets")


class RetryConfig(BaseModel):
    """Configuration for retry logic."""

    max_attempts: int = Field(default=3, description="Maximum retry attempts")
    base_delay: float = Field(default=1.0, description="Base delay in seconds")
    max_delay: float = Field(default=60.0, description="Maximum delay in seconds")
    exponential_base: float = Field(default=2.0, description="Exponential backoff base")
    jitter: bool = Field(default=True, description="Whether to add random jitter")
