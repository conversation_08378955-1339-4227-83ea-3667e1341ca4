"""
Response validation and error handling utilities for the AI Navigator API client.
Provides comprehensive validation for API responses and standardized error handling.
"""

import re
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import ValidationError as PydanticValidationError

from arep.utils.logger import get_logger

from .models import APIError, APIResponse, Resource, ResourceSubmissionResponse

logger = get_logger(__name__)


class ResponseValidationError(Exception):
    """Raised when API response validation fails."""

    pass


class ResponseValidator:
    """
    Validates API responses and ensures data integrity.
    Provides methods for validating different types of responses and extracting errors.
    """

    @staticmethod
    def validate_response_structure(response_data: Dict[str, Any]) -> bool:
        """
        Validate basic response structure.

        Args:
            response_data: Raw response data from API

        Returns:
            True if structure is valid

        Raises:
            ResponseValidationError: If structure is invalid
        """
        if not isinstance(response_data, dict):
            raise ResponseValidationError("Response must be a dictionary")

        # Check for required fields in error responses
        if "error" in response_data or "errors" in response_data:
            if not response_data.get("message"):
                logger.warning("Error response missing message field")

        return True

    @staticmethod
    def validate_entity_id(entity_id: Any) -> UUID:
        """
        Validate and convert entity ID to UUID.

        Args:
            entity_id: Entity ID to validate

        Returns:
            Validated UUID object

        Raises:
            ResponseValidationError: If ID is invalid
        """
        if entity_id is None:
            raise ResponseValidationError("Entity ID cannot be None")

        try:
            if isinstance(entity_id, str):
                return UUID(entity_id)
            elif isinstance(entity_id, UUID):
                return entity_id
            else:
                raise ResponseValidationError(
                    f"Invalid entity ID type: {type(entity_id)}"
                )
        except ValueError as e:
            raise ResponseValidationError(f"Invalid UUID format: {entity_id}") from e

    @staticmethod
    def validate_status(status: Any) -> str:
        """
        Validate entity status.

        Args:
            status: Status to validate

        Returns:
            Validated status string

        Raises:
            ResponseValidationError: If status is invalid
        """
        if not isinstance(status, str):
            raise ResponseValidationError(
                f"Status must be a string, got {type(status)}"
            )

        valid_statuses = {
            "PENDING",
            "APPROVED",
            "REJECTED",
            "DRAFT",
            "PUBLISHED",
            "ARCHIVED",
            "UNDER_REVIEW",
            "NEEDS_REVISION",
        }

        if status.upper() not in valid_statuses:
            logger.warning(f"Unknown status: {status}")

        return status

    @staticmethod
    def validate_url(url: Any, field_name: str = "URL") -> Optional[str]:
        """
        Validate URL format.

        Args:
            url: URL to validate
            field_name: Name of the field for error messages

        Returns:
            Validated URL string or None

        Raises:
            ResponseValidationError: If URL is invalid
        """
        if url is None:
            return None

        if not isinstance(url, str):
            raise ResponseValidationError(f"{field_name} must be a string")

        # Basic URL validation
        url_pattern = re.compile(
            r"^https?://"  # http:// or https://
            r"(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|"  # domain...
            r"localhost|"  # localhost...
            r"\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})"  # ...or ip
            r"(?::\d+)?"  # optional port
            r"(?:/?|[/?]\S+)$",
            re.IGNORECASE,
        )

        if not url_pattern.match(url):
            raise ResponseValidationError(f"Invalid {field_name} format: {url}")

        return url

    @staticmethod
    def extract_errors(response_data: Dict[str, Any]) -> List[APIError]:
        """
        Extract and validate errors from API response.

        Args:
            response_data: Raw response data

        Returns:
            List of APIError objects
        """
        errors = []

        # Handle single error
        if "error" in response_data:
            error_data = response_data["error"]
            if isinstance(error_data, str):
                errors.append(
                    APIError(
                        error="api_error",
                        message=error_data,
                    )
                )
            elif isinstance(error_data, dict):
                errors.append(
                    APIError(
                        error=error_data.get("type", "api_error"),
                        message=error_data.get("message", "Unknown error"),
                        details=error_data.get("details"),
                    )
                )

        # Handle multiple errors
        if "errors" in response_data:
            error_list = response_data["errors"]
            if isinstance(error_list, list):
                for error_item in error_list:
                    if isinstance(error_item, str):
                        errors.append(
                            APIError(
                                error="validation_error",
                                message=error_item,
                            )
                        )
                    elif isinstance(error_item, dict):
                        errors.append(
                            APIError(
                                error=error_item.get("type", "validation_error"),
                                message=error_item.get("message", "Unknown error"),
                                details=error_item.get("details"),
                            )
                        )

        # Handle validation errors (422 responses)
        if "detail" in response_data:
            detail = response_data["detail"]
            if isinstance(detail, list):
                for detail_item in detail:
                    if isinstance(detail_item, dict):
                        field = ".".join(str(loc) for loc in detail_item.get("loc", []))
                        message = detail_item.get("msg", "Validation error")
                        errors.append(
                            APIError(
                                error="validation_error",
                                message=f"{field}: {message}" if field else message,
                                details=detail_item,
                            )
                        )
            elif isinstance(detail, str):
                errors.append(
                    APIError(
                        error="validation_error",
                        message=detail,
                    )
                )

        return errors

    @classmethod
    def validate_submission_response(
        cls, response_data: Dict[str, Any], expected_resource_name: Optional[str] = None
    ) -> ResourceSubmissionResponse:
        """
        Validate and parse a resource submission response.

        Args:
            response_data: Raw response data from API
            expected_resource_name: Expected resource name for validation

        Returns:
            Validated ResourceSubmissionResponse

        Raises:
            ResponseValidationError: If response is invalid
        """
        cls.validate_response_structure(response_data)

        # Extract basic response info
        success = response_data.get("success", True)  # Assume success if not specified
        message = response_data.get("message", "Resource submitted successfully")

        # Extract errors if any
        errors = cls.extract_errors(response_data)
        if errors:
            success = False

        # Extract entity information
        entity_id = None
        status = None

        # Try different possible locations for entity ID
        for id_field in ["id", "entity_id", "uuid"]:
            if id_field in response_data:
                try:
                    entity_id = cls.validate_entity_id(response_data[id_field])
                    break
                except ResponseValidationError:
                    continue

        # Try different possible locations for status
        for status_field in ["status", "state"]:
            if status_field in response_data:
                try:
                    status = cls.validate_status(response_data[status_field])
                    break
                except ResponseValidationError:
                    continue

        # Validate resource name if provided
        if expected_resource_name and "name" in response_data:
            response_name = response_data["name"]
            if response_name != expected_resource_name:
                logger.warning(
                    f"Resource name mismatch: expected '{expected_resource_name}', "
                    f"got '{response_name}'"
                )

        return ResourceSubmissionResponse(
            success=success,
            message=message,
            data=response_data,
            errors=errors if errors else None,
            entity_id=entity_id,
            status=status,
        )

    @classmethod
    def validate_resource_data(cls, resource: Resource) -> List[str]:
        """
        Validate resource data before submission.

        Args:
            resource: Resource object to validate

        Returns:
            List of validation error messages
        """
        errors = []

        try:
            # Use Pydantic's built-in validation
            resource.model_validate(resource.model_dump())
        except PydanticValidationError as e:
            for error in e.errors():
                field = ".".join(str(loc) for loc in error["loc"])
                message = error["msg"]
                errors.append(f"{field}: {message}")

        # Additional custom validations
        if resource.name and len(resource.name.strip()) < 3:
            errors.append("name: Must be at least 3 characters long")

        if resource.short_description and len(resource.short_description.strip()) < 10:
            errors.append("short_description: Must be at least 10 characters long")

        # Validate URLs
        try:
            if resource.website_url:
                cls.validate_url(str(resource.website_url), "website_url")
        except ResponseValidationError as e:
            errors.append(str(e))

        try:
            if resource.logo_url:
                cls.validate_url(str(resource.logo_url), "logo_url")
        except ResponseValidationError as e:
            errors.append(str(e))

        return errors

    @staticmethod
    def sanitize_response_data(response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize response data by removing sensitive information and normalizing fields.

        Args:
            response_data: Raw response data

        Returns:
            Sanitized response data
        """
        # Create a copy to avoid modifying the original
        sanitized = response_data.copy()

        # Remove sensitive fields
        sensitive_fields = ["api_key", "token", "password", "secret"]
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = "[REDACTED]"

        # Normalize common fields
        if "id" in sanitized and isinstance(sanitized["id"], str):
            try:
                # Ensure ID is a valid UUID string
                UUID(sanitized["id"])
            except ValueError:
                logger.warning(f"Invalid UUID in response: {sanitized['id']}")

        return sanitized
