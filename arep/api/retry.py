"""
Advanced retry logic and backoff strategies for the AI Navigator API client.
Provides configurable retry mechanisms with exponential backoff, jitter, and custom conditions.
"""

import asyncio
import random
import time
from typing import Any, Callable, Optional, Type, Union

import aiohttp
from tenacity import (after_log, before_sleep_log, retry,
                      retry_if_exception_type, retry_if_result,
                      stop_after_attempt, stop_after_delay, wait_combine,
                      wait_exponential, wait_fixed, wait_random)

from arep.utils.logger import get_logger

from .client import (APIClientError, AuthenticationError, RateLimitError,
                     ValidationError)

logger = get_logger(__name__)


class RetryStrategy:
    """
    Configurable retry strategy for API requests.
    Provides different retry patterns and backoff strategies.
    """

    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        exponential_base: float = 2.0,
        jitter: bool = True,
        retry_on_status_codes: Optional[list] = None,
        stop_on_status_codes: Optional[list] = None,
    ):
        """
        Initialize retry strategy.

        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay in seconds
            max_delay: Maximum delay in seconds
            exponential_base: Base for exponential backoff
            jitter: Whether to add random jitter to delays
            retry_on_status_codes: HTTP status codes to retry on
            stop_on_status_codes: HTTP status codes to never retry on
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.exponential_base = exponential_base
        self.jitter = jitter

        # Default retry on server errors and rate limits
        self.retry_on_status_codes = retry_on_status_codes or [429, 500, 502, 503, 504]

        # Never retry on client errors (except rate limits)
        self.stop_on_status_codes = stop_on_status_codes or [400, 401, 403, 404, 422]

    def should_retry_exception(self, exception: Exception) -> bool:
        """
        Determine if an exception should trigger a retry.

        Args:
            exception: Exception that occurred

        Returns:
            True if should retry, False otherwise
        """
        # Never retry authentication errors
        if isinstance(exception, AuthenticationError):
            return False

        # Never retry validation errors
        if isinstance(exception, ValidationError):
            return False

        # Retry rate limit errors
        if isinstance(exception, RateLimitError):
            return True

        # Retry general API client errors (usually network/server issues)
        if isinstance(exception, APIClientError):
            return True

        # Retry aiohttp client errors (network issues)
        if isinstance(exception, aiohttp.ClientError):
            return True

        # Don't retry other exceptions by default
        return False

    def get_wait_strategy(self):
        """
        Get the wait strategy for tenacity.

        Returns:
            Tenacity wait strategy
        """
        if self.jitter:
            # Exponential backoff with jitter
            return wait_combine(
                wait_exponential(
                    multiplier=self.base_delay,
                    max=self.max_delay,
                    exp_base=self.exponential_base,
                ),
                wait_random(0, 1),  # Add up to 1 second of jitter
            )
        else:
            # Pure exponential backoff
            return wait_exponential(
                multiplier=self.base_delay,
                max=self.max_delay,
                exp_base=self.exponential_base,
            )

    def get_stop_strategy(self):
        """
        Get the stop strategy for tenacity.

        Returns:
            Tenacity stop strategy
        """
        return stop_after_attempt(self.max_attempts)

    def get_retry_condition(self):
        """
        Get the retry condition for tenacity.

        Returns:
            Tenacity retry condition
        """
        return retry_if_exception_type(
            (
                APIClientError,
                RateLimitError,
                aiohttp.ClientError,
            )
        ) & ~retry_if_exception_type(
            (
                AuthenticationError,
                ValidationError,
            )
        )


class AdaptiveRetryStrategy(RetryStrategy):
    """
    Adaptive retry strategy that adjusts based on success/failure patterns.
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.success_count = 0
        self.failure_count = 0
        self.consecutive_failures = 0
        self.last_success_time = time.time()

    def record_success(self):
        """Record a successful request."""
        self.success_count += 1
        self.consecutive_failures = 0
        self.last_success_time = time.time()

        # Reduce delays if we're having consistent success
        if self.success_count % 10 == 0 and self.base_delay > 0.5:
            self.base_delay *= 0.9
            logger.debug(
                f"Reduced base delay to {self.base_delay:.2f}s due to success pattern"
            )

    def record_failure(self):
        """Record a failed request."""
        self.failure_count += 1
        self.consecutive_failures += 1

        # Increase delays if we're having consecutive failures
        if self.consecutive_failures >= 3 and self.base_delay < 10.0:
            self.base_delay *= 1.5
            logger.debug(
                f"Increased base delay to {self.base_delay:.2f}s due to failure pattern"
            )

    def get_adaptive_delay(self, attempt_number: int) -> float:
        """
        Calculate adaptive delay based on current conditions.

        Args:
            attempt_number: Current attempt number

        Returns:
            Delay in seconds
        """
        base_delay = self.base_delay * (self.exponential_base ** (attempt_number - 1))

        # Add extra delay if we've had recent failures
        if self.consecutive_failures > 0:
            failure_multiplier = 1 + (self.consecutive_failures * 0.5)
            base_delay *= failure_multiplier

        # Add jitter if enabled
        if self.jitter:
            jitter_amount = random.uniform(0, min(base_delay * 0.1, 1.0))
            base_delay += jitter_amount

        return min(base_delay, self.max_delay)


def create_retry_decorator(strategy: RetryStrategy):
    """
    Create a retry decorator using the specified strategy.

    Args:
        strategy: RetryStrategy instance

    Returns:
        Tenacity retry decorator
    """
    return retry(
        stop=strategy.get_stop_strategy(),
        wait=strategy.get_wait_strategy(),
        retry=strategy.get_retry_condition(),
        before_sleep=before_sleep_log(logger, log_level="WARNING"),
        after=after_log(logger, log_level="INFO"),
        reraise=True,
    )


# Predefined retry strategies

# Conservative strategy for production
CONSERVATIVE_RETRY = RetryStrategy(
    max_attempts=3,
    base_delay=2.0,
    max_delay=30.0,
    exponential_base=2.0,
    jitter=True,
)

# Aggressive strategy for development/testing
AGGRESSIVE_RETRY = RetryStrategy(
    max_attempts=5,
    base_delay=0.5,
    max_delay=60.0,
    exponential_base=2.0,
    jitter=True,
)

# Minimal strategy for time-sensitive operations
MINIMAL_RETRY = RetryStrategy(
    max_attempts=2,
    base_delay=1.0,
    max_delay=5.0,
    exponential_base=2.0,
    jitter=False,
)

# Rate limit specific strategy
RATE_LIMIT_RETRY = RetryStrategy(
    max_attempts=10,
    base_delay=5.0,
    max_delay=300.0,  # 5 minutes max
    exponential_base=1.5,
    jitter=True,
    retry_on_status_codes=[429],
    stop_on_status_codes=[400, 401, 403, 404, 422, 500, 502, 503, 504],
)


class CircuitBreaker:
    """
    Circuit breaker pattern implementation for API requests.
    Prevents cascading failures by temporarily stopping requests when error rate is high.
    """

    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: float = 60.0,
        expected_exception: Type[Exception] = APIClientError,
    ):
        """
        Initialize circuit breaker.

        Args:
            failure_threshold: Number of failures before opening circuit
            recovery_timeout: Time to wait before attempting recovery
            expected_exception: Exception type to monitor
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception

        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def __call__(self, func: Callable) -> Callable:
        """Decorator to apply circuit breaker to a function."""

        async def wrapper(*args, **kwargs):
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                    logger.info("Circuit breaker entering HALF_OPEN state")
                else:
                    raise APIClientError("Circuit breaker is OPEN - requests blocked")

            try:
                result = await func(*args, **kwargs)
                self._on_success()
                return result

            except self.expected_exception as e:
                self._on_failure()
                raise

        return wrapper

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset."""
        if self.last_failure_time is None:
            return True
        return time.time() - self.last_failure_time >= self.recovery_timeout

    def _on_success(self):
        """Handle successful request."""
        if self.state == "HALF_OPEN":
            self.state = "CLOSED"
            logger.info("Circuit breaker reset to CLOSED state")
        self.failure_count = 0

    def _on_failure(self):
        """Handle failed request."""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"
            logger.warning(
                f"Circuit breaker opened after {self.failure_count} failures. "
                f"Will retry after {self.recovery_timeout} seconds."
            )


# Utility functions


async def retry_with_backoff(
    func: Callable,
    *args,
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    **kwargs,
) -> Any:
    """
    Simple retry function with exponential backoff.

    Args:
        func: Async function to retry
        *args: Arguments for the function
        max_attempts: Maximum retry attempts
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        **kwargs: Keyword arguments for the function

    Returns:
        Function result

    Raises:
        Last exception if all retries fail
    """
    last_exception = None

    for attempt in range(max_attempts):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            last_exception = e

            if attempt == max_attempts - 1:
                # Last attempt, don't wait
                break

            # Calculate delay with exponential backoff
            delay = min(base_delay * (2**attempt), max_delay)

            # Add jitter
            jitter = random.uniform(0, delay * 0.1)
            total_delay = delay + jitter

            logger.warning(
                f"Attempt {attempt + 1}/{max_attempts} failed: {e}. "
                f"Retrying in {total_delay:.2f} seconds..."
            )

            await asyncio.sleep(total_delay)

    # All attempts failed
    raise last_exception
