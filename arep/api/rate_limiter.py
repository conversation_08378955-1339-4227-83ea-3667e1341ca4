"""
Rate limiting implementation for the AI Navigator API client.
Provides various rate limiting strategies to respect API constraints and prevent abuse.
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass
from datetime import datetime, timedelta
from typing import Dict, Optional, Union

from arep.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting."""

    requests_per_second: float = 1.0
    requests_per_minute: int = 60
    requests_per_hour: int = 3600
    burst_size: int = 5
    adaptive: bool = True


class TokenBucket:
    """
    Token bucket rate limiter implementation.
    Allows burst requests up to bucket capacity, then enforces steady rate.
    """

    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket.

        Args:
            capacity: Maximum number of tokens (burst size)
            refill_rate: Rate at which tokens are added (tokens per second)
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
        self._lock = asyncio.Lock()

    async def acquire(self, tokens: int = 1) -> bool:
        """
        Try to acquire tokens from the bucket.

        Args:
            tokens: Number of tokens to acquire

        Returns:
            True if tokens were acquired, False otherwise
        """
        async with self._lock:
            self._refill()

            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False

    async def wait_for_tokens(self, tokens: int = 1) -> float:
        """
        Wait until enough tokens are available.

        Args:
            tokens: Number of tokens needed

        Returns:
            Time waited in seconds
        """
        start_time = time.time()

        while True:
            if await self.acquire(tokens):
                return time.time() - start_time

            # Calculate how long to wait for next token
            wait_time = 1.0 / self.refill_rate
            await asyncio.sleep(min(wait_time, 0.1))  # Check at least every 100ms

    def _refill(self):
        """Refill tokens based on elapsed time."""
        now = time.time()
        elapsed = now - self.last_refill

        # Add tokens based on elapsed time
        tokens_to_add = elapsed * self.refill_rate
        self.tokens = min(self.capacity, self.tokens + tokens_to_add)
        self.last_refill = now

    @property
    def available_tokens(self) -> float:
        """Get current number of available tokens."""
        self._refill()
        return self.tokens


class SlidingWindowRateLimiter:
    """
    Sliding window rate limiter implementation.
    Tracks requests in a sliding time window.
    """

    def __init__(self, max_requests: int, window_size: float):
        """
        Initialize sliding window rate limiter.

        Args:
            max_requests: Maximum requests allowed in window
            window_size: Window size in seconds
        """
        self.max_requests = max_requests
        self.window_size = window_size
        self.requests = deque()
        self._lock = asyncio.Lock()

    async def is_allowed(self) -> bool:
        """
        Check if a request is allowed.

        Returns:
            True if request is allowed, False otherwise
        """
        async with self._lock:
            now = time.time()

            # Remove old requests outside the window
            while self.requests and self.requests[0] <= now - self.window_size:
                self.requests.popleft()

            # Check if we can make another request
            if len(self.requests) < self.max_requests:
                self.requests.append(now)
                return True

            return False

    async def wait_until_allowed(self) -> float:
        """
        Wait until a request is allowed.

        Returns:
            Time waited in seconds
        """
        start_time = time.time()

        while not await self.is_allowed():
            # Wait a short time before checking again
            await asyncio.sleep(0.1)

        return time.time() - start_time

    @property
    def current_usage(self) -> int:
        """Get current number of requests in window."""
        now = time.time()
        # Count requests in current window
        return sum(1 for req_time in self.requests if req_time > now - self.window_size)


class AdaptiveRateLimiter:
    """
    Adaptive rate limiter that adjusts limits based on API responses.
    """

    def __init__(self, initial_config: RateLimitConfig):
        """
        Initialize adaptive rate limiter.

        Args:
            initial_config: Initial rate limit configuration
        """
        self.config = initial_config
        self.token_bucket = TokenBucket(
            capacity=initial_config.burst_size,
            refill_rate=initial_config.requests_per_second,
        )

        # Sliding windows for different time periods
        self.minute_limiter = SlidingWindowRateLimiter(
            max_requests=initial_config.requests_per_minute,
            window_size=60.0,
        )
        self.hour_limiter = SlidingWindowRateLimiter(
            max_requests=initial_config.requests_per_hour,
            window_size=3600.0,
        )

        # Adaptation tracking
        self.success_count = 0
        self.rate_limit_hits = 0
        self.last_adaptation = time.time()
        self.adaptation_interval = 300.0  # 5 minutes

    async def acquire(self) -> float:
        """
        Acquire permission to make a request.

        Returns:
            Time waited in seconds
        """
        start_time = time.time()

        # Check all rate limiters
        tasks = [
            self.token_bucket.wait_for_tokens(1),
            self.minute_limiter.wait_until_allowed(),
            self.hour_limiter.wait_until_allowed(),
        ]

        # Wait for all limiters to allow the request
        await asyncio.gather(*tasks)

        return time.time() - start_time

    def record_success(self):
        """Record a successful request."""
        self.success_count += 1
        self._maybe_adapt()

    def record_rate_limit_hit(self, retry_after: Optional[int] = None):
        """
        Record a rate limit hit.

        Args:
            retry_after: Retry-After header value in seconds
        """
        self.rate_limit_hits += 1

        if retry_after:
            # Temporarily reduce rate based on retry-after
            new_rate = 1.0 / max(retry_after, 1)
            self.token_bucket.refill_rate = min(self.token_bucket.refill_rate, new_rate)
            logger.warning(
                f"Rate limited. Reduced rate to {new_rate:.3f} req/s for {retry_after}s"
            )

        self._maybe_adapt()

    def _maybe_adapt(self):
        """Adapt rate limits based on recent performance."""
        now = time.time()
        if now - self.last_adaptation < self.adaptation_interval:
            return

        if not self.config.adaptive:
            return

        self.last_adaptation = now

        # Calculate success rate
        total_requests = self.success_count + self.rate_limit_hits
        if total_requests == 0:
            return

        success_rate = self.success_count / total_requests

        # Adapt based on success rate
        if success_rate > 0.95 and self.rate_limit_hits == 0:
            # High success rate, try to increase rate slightly
            new_rate = min(
                self.token_bucket.refill_rate * 1.1, self.config.requests_per_second * 2
            )
            self.token_bucket.refill_rate = new_rate
            logger.info(
                f"Increased rate limit to {new_rate:.3f} req/s (success rate: {success_rate:.2%})"
            )

        elif success_rate < 0.8 or self.rate_limit_hits > 0:
            # Low success rate or rate limit hits, decrease rate
            new_rate = max(
                self.token_bucket.refill_rate * 0.8,
                self.config.requests_per_second * 0.1,
            )
            self.token_bucket.refill_rate = new_rate
            logger.warning(
                f"Decreased rate limit to {new_rate:.3f} req/s (success rate: {success_rate:.2%})"
            )

        # Reset counters
        self.success_count = 0
        self.rate_limit_hits = 0

    def get_status(self) -> Dict[str, Union[float, int]]:
        """
        Get current rate limiter status.

        Returns:
            Dictionary with status information
        """
        return {
            "current_rate": self.token_bucket.refill_rate,
            "available_tokens": self.token_bucket.available_tokens,
            "minute_usage": self.minute_limiter.current_usage,
            "minute_limit": self.minute_limiter.max_requests,
            "hour_usage": self.hour_limiter.current_usage,
            "hour_limit": self.hour_limiter.max_requests,
            "success_count": self.success_count,
            "rate_limit_hits": self.rate_limit_hits,
        }


class GlobalRateLimiter:
    """
    Global rate limiter that can be shared across multiple clients.
    """

    _instances: Dict[str, "GlobalRateLimiter"] = {}

    def __new__(cls, name: str = "default", config: Optional[RateLimitConfig] = None):
        """Create or return existing instance."""
        if name not in cls._instances:
            instance = super().__new__(cls)
            cls._instances[name] = instance
            instance._initialized = False
        return cls._instances[name]

    def __init__(self, name: str = "default", config: Optional[RateLimitConfig] = None):
        """Initialize global rate limiter."""
        if self._initialized:
            return

        self.name = name
        self.config = config or RateLimitConfig()
        self.limiter = AdaptiveRateLimiter(self.config)
        self._initialized = True

        logger.info(
            f"Initialized global rate limiter '{name}' with {self.config.requests_per_second} req/s"
        )

    async def acquire(self) -> float:
        """Acquire permission to make a request."""
        return await self.limiter.acquire()

    def record_success(self):
        """Record a successful request."""
        self.limiter.record_success()

    def record_rate_limit_hit(self, retry_after: Optional[int] = None):
        """Record a rate limit hit."""
        self.limiter.record_rate_limit_hit(retry_after)

    def get_status(self) -> Dict[str, Union[float, int]]:
        """Get current status."""
        return self.limiter.get_status()

    @classmethod
    def get_instance(cls, name: str = "default") -> "GlobalRateLimiter":
        """Get existing instance by name."""
        return cls._instances.get(name)

    @classmethod
    def reset_all(cls):
        """Reset all instances (useful for testing)."""
        cls._instances.clear()


# Utility functions


async def rate_limited_request(
    func, rate_limiter: Union[AdaptiveRateLimiter, GlobalRateLimiter], *args, **kwargs
):
    """
    Execute a function with rate limiting.

    Args:
        func: Function to execute
        rate_limiter: Rate limiter to use
        *args: Function arguments
        **kwargs: Function keyword arguments

    Returns:
        Function result
    """
    # Wait for rate limiter permission
    wait_time = await rate_limiter.acquire()

    if wait_time > 0:
        logger.debug(f"Rate limiter delayed request by {wait_time:.2f}s")

    try:
        result = await func(*args, **kwargs)
        rate_limiter.record_success()
        return result

    except Exception as e:
        # Check if it's a rate limit error
        if "rate limit" in str(e).lower() or "429" in str(e):
            # Try to extract retry-after from error message
            retry_after = None
            if hasattr(e, "retry_after"):
                retry_after = e.retry_after
            rate_limiter.record_rate_limit_hit(retry_after)

        raise


def create_rate_limiter(
    requests_per_second: float = 1.0,
    requests_per_minute: int = 60,
    requests_per_hour: int = 3600,
    burst_size: int = 5,
    adaptive: bool = True,
    global_name: Optional[str] = None,
) -> Union[AdaptiveRateLimiter, GlobalRateLimiter]:
    """
    Create a rate limiter with the specified configuration.

    Args:
        requests_per_second: Maximum requests per second
        requests_per_minute: Maximum requests per minute
        requests_per_hour: Maximum requests per hour
        burst_size: Maximum burst size
        adaptive: Whether to use adaptive rate limiting
        global_name: If specified, create/return a global rate limiter

    Returns:
        Rate limiter instance
    """
    config = RateLimitConfig(
        requests_per_second=requests_per_second,
        requests_per_minute=requests_per_minute,
        requests_per_hour=requests_per_hour,
        burst_size=burst_size,
        adaptive=adaptive,
    )

    if global_name:
        return GlobalRateLimiter(global_name, config)
    else:
        return AdaptiveRateLimiter(config)
