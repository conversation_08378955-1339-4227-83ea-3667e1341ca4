"""
Core data models for the AI Resource Enhancement Pipeline.
Defines models for entities as they flow through the collection, classification, and enhancement stages.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, HttpUrl


class MinimalEntity(BaseModel):
    """
    Minimal entity data collected during the initial discovery phase.
    Contains just enough information to identify and classify the entity.
    """
    name: str
    url: HttpUrl
    logo_url: Optional[HttpUrl] = None
    source: str
    discovered_at: datetime


class ClassificationResult(BaseModel):
    """
    Result of entity type classification.
    Contains the determined entity type and confidence score.
    """
    entity_type: str
    entity_type_id: UUID
    confidence: float
    reasoning: Optional[str] = None
    alternative_types: List[str] = []


class ClassifiedEntity(MinimalEntity):
    """
    Entity that has been classified with its type determined.
    Combines minimal entity data with classification results.
    """
    entity_type: str
    entity_type_id: UUID
    classification_confidence: float
    classification_reasoning: Optional[str] = None
    alternative_types: List[str] = []


class ResearchData(BaseModel):
    """
    Research data collected about an entity.
    Contains enhanced information gathered from various sources.
    """
    description: Optional[str] = None
    short_description: Optional[str] = None
    features: List[str] = []
    categories: List[str] = []
    tags: List[str] = []
    pricing_info: Optional[str] = None
    contact_info: Optional[str] = None
    social_links: Optional[dict] = None
    technical_details: Optional[dict] = None
    research_sources: List[str] = []
    research_timestamp: datetime


class EnhancedEntity(ClassifiedEntity):
    """
    Fully enhanced entity ready for API submission.
    Contains all collected and researched data about the entity.
    """
    research_data: ResearchData
    enhancement_version: str = "1.0"
    enhancement_timestamp: datetime
    quality_score: Optional[float] = None
    ready_for_submission: bool = False


class ProcessingStatus(BaseModel):
    """
    Status tracking for entity processing through the pipeline.
    """
    entity_id: str
    current_stage: str  # collected, classified, researched, enhanced, submitted
    status: str  # pending, processing, completed, failed
    error_message: Optional[str] = None
    started_at: datetime
    completed_at: Optional[datetime] = None
    processing_time: Optional[float] = None


class ScrapingResult(BaseModel):
    """
    Result of scraping operation from a specific source.
    """
    source_name: str
    source_url: HttpUrl
    entities_found: int
    entities_data: List[dict]
    scraping_timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[dict] = None


class CollectionMetrics(BaseModel):
    """
    Metrics for tracking collection performance.
    """
    total_entities_discovered: int = 0
    total_entities_classified: int = 0
    total_entities_enhanced: int = 0
    total_entities_submitted: int = 0
    successful_submissions: int = 0
    failed_submissions: int = 0
    average_processing_time: Optional[float] = None
    collection_start_time: Optional[datetime] = None
    collection_end_time: Optional[datetime] = None
