"""
Registry system for automatically discovering and loading BaseCollector subclasses.
Implements the pluggable scraper architecture by dynamically finding all collectors.
"""

import pkgutil
import inspect
import importlib
from typing import List, Type
from pathlib import Path

from arep.collectors.base import BaseCollector
from arep.utils.logger import get_logger

logger = get_logger(__name__)


def get_all_collectors() -> List[BaseCollector]:
    """
    Dynamically finds and instantiates all BaseCollector subclasses.
    
    This function automatically discovers all Python modules in the collectors
    package and looks for BaseCollector subclasses to instantiate.
    
    Returns:
        List of instantiated BaseCollector objects
    """
    collectors = []
    
    # Get the path to the collectors package
    collectors_path = Path(__file__).parent
    
    logger.info("Discovering collectors...")
    
    # Import all modules in the collectors package to trigger class registration
    for module_info in pkgutil.iter_modules([str(collectors_path)]):
        module_name = module_info.name
        
        # Skip the registry module itself and base module
        if module_name in ['registry', 'base', '__init__']:
            continue
            
        try:
            # Import the module
            full_module_name = f"arep.collectors.{module_name}"
            module = importlib.import_module(full_module_name)
            logger.debug(f"Imported module: {full_module_name}")
            
            # Look for BaseCollector subclasses in the module
            for item_name in dir(module):
                item = getattr(module, item_name)
                
                # Check if it's a class and a subclass of BaseCollector
                if (inspect.isclass(item) and 
                    issubclass(item, BaseCollector) and 
                    item is not BaseCollector):
                    
                    try:
                        # Instantiate the collector
                        collector_instance = item()
                        collectors.append(collector_instance)
                        logger.info(f"Registered collector: {collector_instance.source_name} ({item.__name__})")
                        
                    except Exception as e:
                        logger.error(f"Failed to instantiate collector {item.__name__}: {e}")
                        
        except Exception as e:
            logger.error(f"Failed to import module {module_name}: {e}")
    
    logger.info(f"Discovered {len(collectors)} collectors: {[c.source_name for c in collectors]}")
    return collectors


def get_collector_by_name(source_name: str) -> BaseCollector:
    """
    Get a specific collector by its source name.
    
    Args:
        source_name: The source_name of the collector to retrieve
        
    Returns:
        BaseCollector instance
        
    Raises:
        ValueError: If no collector with the given name is found
    """
    collectors = get_all_collectors()
    
    for collector in collectors:
        if collector.source_name == source_name:
            return collector
    
    available_names = [c.source_name for c in collectors]
    raise ValueError(f"No collector found with name '{source_name}'. Available: {available_names}")


def get_collector_classes() -> List[Type[BaseCollector]]:
    """
    Get all BaseCollector subclass types (not instances).
    
    Returns:
        List of BaseCollector subclass types
    """
    collector_classes = []
    
    # Get the path to the collectors package
    collectors_path = Path(__file__).parent
    
    # Import all modules in the collectors package
    for module_info in pkgutil.iter_modules([str(collectors_path)]):
        module_name = module_info.name
        
        # Skip the registry module itself and base module
        if module_name in ['registry', 'base', '__init__']:
            continue
            
        try:
            # Import the module
            full_module_name = f"arep.collectors.{module_name}"
            module = importlib.import_module(full_module_name)
            
            # Look for BaseCollector subclasses in the module
            for item_name in dir(module):
                item = getattr(module, item_name)
                
                # Check if it's a class and a subclass of BaseCollector
                if (inspect.isclass(item) and 
                    issubclass(item, BaseCollector) and 
                    item is not BaseCollector):
                    
                    collector_classes.append(item)
                        
        except Exception as e:
            logger.error(f"Failed to import module {module_name}: {e}")
    
    return collector_classes


def list_available_collectors() -> List[str]:
    """
    Get a list of all available collector source names.
    
    Returns:
        List of source names for all available collectors
    """
    try:
        collectors = get_all_collectors()
        return [collector.source_name for collector in collectors]
    except Exception as e:
        logger.error(f"Error listing collectors: {e}")
        return []


def validate_collectors() -> bool:
    """
    Validate that all discovered collectors are properly configured.
    
    Returns:
        True if all collectors are valid, False otherwise
    """
    try:
        collectors = get_all_collectors()
        
        if not collectors:
            logger.warning("No collectors found")
            return False
        
        valid = True
        for collector in collectors:
            # Check that source_name is set
            if not hasattr(collector, 'source_name') or not collector.source_name:
                logger.error(f"Collector {collector.__class__.__name__} missing source_name")
                valid = False
            
            # Check that collect method exists
            if not hasattr(collector, 'collect') or not callable(collector.collect):
                logger.error(f"Collector {collector.__class__.__name__} missing collect method")
                valid = False
        
        return valid
        
    except Exception as e:
        logger.error(f"Error validating collectors: {e}")
        return False
