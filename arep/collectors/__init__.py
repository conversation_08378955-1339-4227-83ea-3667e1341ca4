"""
Collectors package for the AI Resource Enhancement Pipeline.
Contains all data collection implementations including scrapers and API clients.
"""

from .base import BaseCollector, BaseScraper, MockScraper
from .collector import MinimalDataCollector, AdvancedDataCollector, PluggableDataCollector
from .registry import get_all_collectors, get_collector_by_name, list_available_collectors

# Import all collector modules to ensure they're registered
from . import product_hunt
from . import arxiv

__all__ = [
    "BaseCollector",
    "BaseScraper",
    "MockScraper",
    "MinimalDataCollector",
    "AdvancedDataCollector",
    "PluggableDataCollector",
    "get_all_collectors",
    "get_collector_by_name",
    "list_available_collectors",
]