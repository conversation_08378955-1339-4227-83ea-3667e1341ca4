"""
Base scraper class for all data collection implementations.
Provides a consistent interface and common functionality for all scrapers.
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional

import aiohttp
from bs4 import BeautifulSoup

from arep.models import MinimalEntity, <PERSON>rapingResult
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class BaseCollector(ABC):
    """
    Abstract base class for all data collectors (scrapers and API clients).
    This is the new pluggable interface that follows the architectural plan.
    """
    # Unique identifier for the source, e.g., 'product_hunt'
    source_name: str

    @abstractmethod
    async def collect(self, session: aiohttp.ClientSession) -> List[MinimalEntity]:
        """
        The core method to collect data from the source.
        It must be implemented by every collector subclass.

        Args:
            session: aiohttp ClientSession for making HTTP requests

        Returns:
            List of MinimalEntity objects discovered from the source
        """
        pass

    async def run(self) -> List[MinimalEntity]:
        """
        A standard runner for the collector. It handles session creation
        and provides a consistent entry point.

        Returns:
            List of MinimalEntity objects
        """
        headers = {
            'User-Agent': 'AINavigator-AREP/1.0 (https://ai-navigator.com; <EMAIL>)'
        }
        async with aiohttp.ClientSession(headers=headers) as session:
            return await self.collect(session)


class BaseScraper(ABC):
    """
    Abstract base class for all scrapers.
    Provides common functionality and enforces consistent interface.
    """
    
    def __init__(
        self,
        name: str,
        base_url: str,
        rate_limit_delay: float = 1.0,
        timeout: int = 30,
        max_retries: int = 3,
    ):
        """
        Initialize the base scraper.
        
        Args:
            name: Name of the scraper
            base_url: Base URL for the scraping source
            rate_limit_delay: Delay between requests in seconds
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
        """
        self.name = name
        self.base_url = base_url
        self.rate_limit_delay = rate_limit_delay
        self.timeout = timeout
        self.max_retries = max_retries
        
        # Session management
        self._session: Optional[aiohttp.ClientSession] = None
        self._last_request_time: float = 0
        
        # Metrics
        self.requests_made = 0
        self.successful_requests = 0
        self.failed_requests = 0
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_session(self):
        """Ensure aiohttp session is created."""
        if self._session is None or self._session.closed:
            headers = {
                'User-Agent': 'AI-Resource-Enhancement-Pipeline/1.0 (+https://github.com/ai-nav/scraper)',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self._session = aiohttp.ClientSession(
                headers=headers,
                timeout=timeout,
            )
    
    async def close(self):
        """Close the HTTP session."""
        if self._session and not self._session.closed:
            await self._session.close()
            self._session = None
    
    async def _rate_limit(self):
        """Enforce rate limiting between requests."""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            logger.debug(f"Rate limiting: sleeping for {sleep_time:.2f} seconds")
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = asyncio.get_event_loop().time()
    
    async def _make_request(self, url: str, **kwargs) -> Optional[aiohttp.ClientResponse]:
        """
        Make an HTTP request with rate limiting and retry logic.
        
        Args:
            url: URL to request
            **kwargs: Additional arguments for aiohttp request
            
        Returns:
            Response object or None if failed
        """
        await self._ensure_session()
        await self._rate_limit()
        
        for attempt in range(self.max_retries):
            try:
                self.requests_made += 1
                logger.debug(f"Making request to {url} (attempt {attempt + 1})")
                
                async with self._session.get(url, **kwargs) as response:
                    if response.status == 200:
                        self.successful_requests += 1
                        return response
                    elif response.status == 429:  # Rate limited
                        retry_after = int(response.headers.get('Retry-After', 60))
                        logger.warning(f"Rate limited, waiting {retry_after} seconds")
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        logger.warning(f"HTTP {response.status} for {url}")
                        if attempt == self.max_retries - 1:
                            self.failed_requests += 1
                            return None
                        
            except asyncio.TimeoutError:
                logger.warning(f"Timeout for {url} (attempt {attempt + 1})")
                if attempt == self.max_retries - 1:
                    self.failed_requests += 1
                    return None
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                
            except Exception as e:
                logger.error(f"Error requesting {url}: {e}")
                if attempt == self.max_retries - 1:
                    self.failed_requests += 1
                    return None
                await asyncio.sleep(2 ** attempt)
        
        return None
    
    async def _fetch_page_content(self, url: str) -> Optional[str]:
        """
        Fetch the HTML content of a page.
        
        Args:
            url: URL to fetch
            
        Returns:
            HTML content or None if failed
        """
        response = await self._make_request(url)
        if response:
            try:
                content = await response.text()
                return content
            except Exception as e:
                logger.error(f"Error reading content from {url}: {e}")
        return None
    
    async def _parse_html(self, html: str) -> Optional[BeautifulSoup]:
        """
        Parse HTML content using BeautifulSoup.
        
        Args:
            html: HTML content to parse
            
        Returns:
            BeautifulSoup object or None if parsing failed
        """
        try:
            return BeautifulSoup(html, 'html.parser')
        except Exception as e:
            logger.error(f"Error parsing HTML: {e}")
            return None
    
    def _extract_favicon_url(self, url: str) -> str:
        """
        Generate a favicon URL using Google's favicon service.
        
        Args:
            url: Website URL
            
        Returns:
            Favicon URL
        """
        from urllib.parse import urlparse
        
        parsed_url = urlparse(url)
        domain = parsed_url.netloc or parsed_url.path
        return f"https://www.google.com/s2/favicons?domain={domain}&sz=128"
    
    def _create_minimal_entity(
        self,
        name: str,
        url: str,
        logo_url: Optional[str] = None,
        **kwargs
    ) -> MinimalEntity:
        """
        Create a MinimalEntity from scraped data.
        
        Args:
            name: Entity name
            url: Entity URL
            logo_url: Logo URL (optional)
            **kwargs: Additional metadata
            
        Returns:
            MinimalEntity object
        """
        return MinimalEntity(
            name=name.strip(),
            url=url,
            logo_url=logo_url or self._extract_favicon_url(url),
            source=self.name,
            discovered_at=datetime.utcnow(),
        )
    
    @abstractmethod
    async def scrape(self) -> ScrapingResult:
        """
        Perform the scraping operation.
        
        Returns:
            ScrapingResult with discovered entities
        """
        pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get scraping metrics.
        
        Returns:
            Dictionary with scraping metrics
        """
        success_rate = 0.0
        if self.requests_made > 0:
            success_rate = (self.successful_requests / self.requests_made) * 100
        
        return {
            'scraper_name': self.name,
            'requests_made': self.requests_made,
            'successful_requests': self.successful_requests,
            'failed_requests': self.failed_requests,
            'success_rate_percent': round(success_rate, 2),
        }


class MockScraper(BaseScraper):
    """
    Mock scraper for testing and development.
    Returns predefined test data.
    """
    
    def __init__(self):
        super().__init__(
            name="mock_scraper",
            base_url="https://example.com",
            rate_limit_delay=0.1,  # Faster for testing
        )
    
    async def scrape(self) -> ScrapingResult:
        """Return mock data for testing."""
        mock_data = [
            {
                "name": "Perplexity AI",
                "url": "https://perplexity.ai",
                "description": "AI-powered search and answer engine"
            },
            {
                "name": "OpenAI",
                "url": "https://openai.com",
                "description": "AI research and deployment company"
            },
            {
                "name": "Anthropic",
                "url": "https://anthropic.com",
                "description": "AI safety company developing helpful, harmless, and honest AI"
            },
            {
                "name": "Hugging Face",
                "url": "https://huggingface.co",
                "description": "Platform for machine learning models and datasets"
            },
        ]
        
        entities_data = []
        for item in mock_data:
            entity = self._create_minimal_entity(
                name=item["name"],
                url=item["url"],
                description=item.get("description"),
            )
            entities_data.append(entity.model_dump())
        
        return ScrapingResult(
            source_name=self.name,
            source_url=self.base_url,
            entities_found=len(entities_data),
            entities_data=entities_data,
            scraping_timestamp=datetime.utcnow(),
            success=True,
            metadata={"mock_data": True},
        )
