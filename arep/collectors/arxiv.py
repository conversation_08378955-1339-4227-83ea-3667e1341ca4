"""
ArXiv API client for collecting recent AI research papers.
Uses the arXiv API to fetch recent papers in AI and machine learning categories.
"""

import aiohttp
from typing import List
from bs4 import BeautifulSoup  # arXiv API returns XML
from datetime import datetime

from arep.collectors.base import BaseCollector
from arep.models import MinimalEntity
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ArxivApiClient(BaseCollector):
    """
    API client for arXiv to collect recent AI research papers.
    Fetches papers from computer science AI category.
    """
    
    source_name = "arxiv"
    # Search for recent AI papers, get 25 results
    API_URL = "http://export.arxiv.org/api/query?search_query=cat:cs.AI&sortBy=submittedDate&sortOrder=descending&max_results=25"

    async def collect(self, session: aiohttp.ClientSession) -> List[MinimalEntity]:
        """
        Collect data from arXiv API.
        
        Args:
            session: aiohttp ClientSession for making requests
            
        Returns:
            List of MinimalEntity objects discovered from arXiv
        """
        logger.info(f"Collecting data from {self.source_name} API")
        try:
            async with session.get(self.API_URL) as response:
                if response.status != 200:
                    logger.error(f"arXiv API returned status {response.status}")
                    return []
                
                xml_data = await response.text()
                soup = BeautifulSoup(xml_data, 'xml')  # Use the XML parser
                
                entities = []
                entries = soup.find_all('entry')

                for entry in entries:
                    try:
                        # Extract title
                        title_tag = entry.find('title')
                        if not title_tag:
                            continue
                        title = title_tag.get_text(strip=True)
                        
                        # Extract URL - the 'id' tag contains the paper URL
                        id_tag = entry.find('id')
                        if not id_tag:
                            continue
                        url = id_tag.get_text(strip=True)
                        
                        # Extract publication date
                        published_tag = entry.find('published')
                        discovered_at = datetime.utcnow()
                        if published_tag:
                            try:
                                # Parse ISO format date from arXiv
                                published_text = published_tag.get_text(strip=True)
                                # Remove 'Z' and add timezone info for proper parsing
                                if published_text.endswith('Z'):
                                    published_text = published_text[:-1] + '+00:00'
                                discovered_at = datetime.fromisoformat(published_text)
                            except Exception as e:
                                logger.debug(f"Error parsing date {published_text}: {e}")
                                # Fall back to current time
                                discovered_at = datetime.utcnow()
                        
                        # Skip if title is too short
                        if len(title) < 10:
                            continue
                        
                        # Clean up title - remove extra whitespace and newlines
                        title = ' '.join(title.split())
                        
                        entities.append(MinimalEntity(
                            name=title,
                            url=url,
                            source=self.source_name,
                            discovered_at=discovered_at
                        ))
                        
                    except Exception as e:
                        logger.debug(f"Error processing arXiv entry: {e}")
                        continue

                logger.info(f"Collected {len(entities)} entities from {self.source_name}")
                return entities

        except Exception as e:
            logger.exception(f"An error occurred calling {self.source_name} API: {e}")
            return []
