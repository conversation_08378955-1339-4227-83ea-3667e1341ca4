"""
Enhancer registry for AI Resource Enhancement Pipeline.
Manages and provides access to type-specific enhancers.
"""

from typing import Dict, Optional, Type
from arep.enhancement.base import BaseEnhancer
from arep.enhancement.tool_enhancer import ToolEnhancer
from arep.enhancement.course_enhancer import CourseEnhancer
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class EnhancerRegistry:
    """
    Registry for managing entity type-specific enhancers.
    
    Provides centralized access to enhancers and supports
    dynamic registration of new enhancer types.
    """
    
    def __init__(self):
        self._enhancers: Dict[str, BaseEnhancer] = {}
        self._enhancer_classes: Dict[str, Type[BaseEnhancer]] = {}
        self._register_default_enhancers()
    
    def _register_default_enhancers(self):
        """Register default enhancers for common entity types."""
        # Register enhancer classes
        self._enhancer_classes.update({
            'tool': ToolEnhancer,
            'course': CourseEnhancer,
            # Add more enhancers as they're implemented
        })
        
        # Initialize enhancers
        for entity_type, enhancer_class in self._enhancer_classes.items():
            try:
                self._enhancers[entity_type] = enhancer_class()
                logger.debug(f"Registered enhancer for entity type: {entity_type}")
            except Exception as e:
                logger.error(f"Failed to register enhancer for {entity_type}: {e}")
    
    def get_enhancer(self, entity_type: str) -> Optional[BaseEnhancer]:
        """
        Get enhancer for a specific entity type.
        
        Args:
            entity_type: The entity type to get enhancer for
            
        Returns:
            BaseEnhancer instance or None if not found
        """
        enhancer = self._enhancers.get(entity_type)
        if not enhancer:
            logger.warning(f"No enhancer found for entity type: {entity_type}")
        return enhancer
    
    def register_enhancer(self, entity_type: str, enhancer: BaseEnhancer):
        """
        Register a new enhancer for an entity type.
        
        Args:
            entity_type: The entity type
            enhancer: The enhancer instance
        """
        self._enhancers[entity_type] = enhancer
        logger.info(f"Registered custom enhancer for entity type: {entity_type}")
    
    def register_enhancer_class(self, entity_type: str, enhancer_class: Type[BaseEnhancer]):
        """
        Register a new enhancer class for an entity type.
        
        Args:
            entity_type: The entity type
            enhancer_class: The enhancer class
        """
        try:
            enhancer = enhancer_class()
            self.register_enhancer(entity_type, enhancer)
            self._enhancer_classes[entity_type] = enhancer_class
        except Exception as e:
            logger.error(f"Failed to register enhancer class for {entity_type}: {e}")
            raise
    
    def get_supported_types(self) -> list:
        """
        Get list of supported entity types.
        
        Returns:
            List of supported entity type strings
        """
        return list(self._enhancers.keys())
    
    def has_enhancer(self, entity_type: str) -> bool:
        """
        Check if enhancer exists for entity type.
        
        Args:
            entity_type: The entity type to check
            
        Returns:
            True if enhancer exists, False otherwise
        """
        return entity_type in self._enhancers


# Global registry instance
enhancer_registry = EnhancerRegistry()
